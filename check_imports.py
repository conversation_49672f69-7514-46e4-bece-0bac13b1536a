#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查修复后的导入是否正常
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def check_imports():
    """检查关键模块导入"""
    print("🔍 检查修复后的导入...")
    
    try:
        # 检查异常类导入
        from app.utils.exceptions import ParameterException
        print("✅ ParameterException 导入成功")
        
        # 检查表单模型导入
        from app.form.trajectory import TrajectoryClassificationBody
        print("✅ TrajectoryClassificationBody 导入成功")
        
        # 尝试创建一个表单实例来验证Pydantic配置
        test_data = {
            "ship_name": "测试舰船",
            "trajectory_coords": [[35.0, 139.0], [35.1, 139.1]],
            "top_k": 5
        }
        form = TrajectoryClassificationBody(**test_data)
        print("✅ Pydantic 模型创建成功")
        print(f"   舰船名称: {form.ship_name}")
        print(f"   坐标点数: {len(form.trajectory_coords)}")
        
        # 检查API模块导入
        from app.api.trajectory import api
        print("✅ trajectory API 模块导入成功")
        
        print("\n🎉 所有导入检查都通过了！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🧪 导入修复验证工具")
    print("=" * 40)
    
    success = check_imports()
    
    if success:
        print("\n✅ 修复成功！现在可以正常启动API服务了。")
        print("运行命令: python start_api_server.py")
    else:
        print("\n❌ 还有问题需要解决。")