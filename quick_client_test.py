#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速客户端测试脚本
避免首次分类的长时间等待，专注于测试API连接和基本功能
"""

import sys
import os
import requests
import json
import time

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入项目自带的API客户端
from app.api.trajectory_usage_example import TrajectoryAPI, create_custom_trajectory

def quick_test():
    """快速测试API基本功能"""
    
    print("⚡ 航迹行为识别API快速测试")
    print("=" * 50)
    
    # 初始化API客户端
    api = TrajectoryAPI(base_url="http://localhost:5000")
    
    # 1. 健康检查
    print("\n1️⃣ 健康检查")
    print("-" * 20)
    
    health_result = api.health_check()
    if isinstance(health_result, dict) and 'error' not in health_result:
        print("✅ 健康检查成功")
        data = health_result.get('data', health_result)
        if isinstance(data, dict):
            print(f"   系统状态: {data.get('status', 'unknown')}")
            model_status = data.get('model_status', {})
            if isinstance(model_status, dict):
                available_models = [k for k, v in model_status.items() if v == "available"]
                print(f"   可用模型: {len(available_models)} 个")
                for model in available_models[:3]:  # 只显示前3个
                    print(f"     ✅ {model}")
    else:
        print(f"❌ 健康检查失败")
    
    # 2. 数据库信息
    print("\n2️⃣ 数据库信息")
    print("-" * 20)
    
    db_info = api.get_database_info()
    if isinstance(db_info, dict) and 'error' not in db_info:
        print("✅ 数据库信息查询成功")
        data = db_info.get('data', db_info)
        if isinstance(data, dict):
            database_info = data.get('database_info', {})
            if isinstance(database_info, dict):
                print(f"   数据库类型: {database_info.get('database_type', 'unknown')}")
                print(f"   缓存数据库数: {database_info.get('cached_databases', 0)}")
    else:
        print(f"❌ 数据库信息查询失败")
    
    # 3. 测试简单分类（如果数据库已存在）
    print("\n3️⃣ 测试分类（如果数据库已存在）")
    print("-" * 30)
    
    # 检查是否有缓存的数据库
    if isinstance(db_info, dict) and 'error' not in db_info:
        data = db_info.get('data', db_info)
        if isinstance(data, dict):
            database_info = data.get('database_info', {})
            cached_dbs = database_info.get('cached_databases', 0)
            
            if cached_dbs > 0:
                print("✅ 检测到已缓存的数据库，进行快速分类测试...")
                
                # 创建简单的测试数据
                test_trajectory = create_custom_trajectory(35.0, 139.0, "linear", 4)
                
                try:
                    start_time = time.time()
                    result = requests.post(
                        f"{api.base_url}{api.api_prefix}/classify",
                        json={
                            "ship_name": "文森号航空母舰",
                            "trajectory_coords": test_trajectory,
                            "top_k": 3
                        },
                        headers=api.headers,
                        timeout=30  # 30秒超时
                    ).json()
                    elapsed_time = int((time.time() - start_time) * 1000)
                    
                    if isinstance(result, dict) and 'error' not in result:
                        data = result.get('data', result)
                        if isinstance(data, dict) and data.get('success', False):
                            print(f"   ✅ 分类成功 (耗时: {elapsed_time}ms)")
                            print(f"      预测行为: {data.get('predicted_action', 'unknown')}")
                            print(f"      置信度: {data.get('confidence', 0):.1%}")
                        else:
                            print(f"   ⚠️ 分类请求成功，但结果异常")
                    else:
                        print(f"   ⚠️ 分类请求失败")
                        
                except requests.exceptions.Timeout:
                    print("   ⚠️ 分类请求超时（可能需要构建数据库）")
                except Exception as e:
                    print(f"   ⚠️ 分类请求异常: {e}")
            else:
                print("ℹ️ 未检测到缓存数据库，跳过分类测试")
                print("   首次分类需要构建向量数据库，可能需要几分钟")
                print("   建议先运行一次完整测试来构建数据库")
    
    # 4. 错误处理测试
    print("\n4️⃣ 错误处理测试")
    print("-" * 20)
    
    print("   测试无效坐标...")
    invalid_coords = [[200, 300], [35.1234, 139.5678]]  # 超出范围的坐标
    
    try:
        result = api.classify_trajectory("测试舰船", invalid_coords)
        if isinstance(result, dict):
            if 'error' in result:
                print("   ✅ 客户端正确捕获了错误")
            else:
                data = result.get('data', result)
                if isinstance(data, dict) and not data.get('success', True):
                    print("   ✅ 服务器正确拒绝了无效坐标")
                else:
                    print("   ⚠️ 无效坐标未被正确处理")
        else:
            print("   ⚠️ 响应格式异常")
    except Exception as e:
        print(f"   ⚠️ 错误处理测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 快速测试总结")
    print("=" * 50)
    print("✅ 基础功能测试完成")
    print("💡 提示:")
    print("   - 如果想测试完整分类功能，请运行: python demo_api_client.py")
    print("   - 首次分类会构建向量数据库，需要1-5分钟")
    print("   - 数据库构建完成后，后续分类会很快")

def check_server():
    """检查服务器是否运行"""
    try:
        response = requests.get("http://localhost:5000/api/v1/trajectory/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    print("🚀 启动快速测试...")
    
    # 检查服务器状态
    if not check_server():
        print("❌ 无法连接到API服务器")
        print("请确保服务器正在运行: python start_api_server.py")
        return
    
    print("✅ 服务器连接正常")
    
    # 运行快速测试
    quick_test()

if __name__ == "__main__":
    main()