#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航迹行为识别API服务启动脚本
简化API服务的启动过程
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'flask',
        'flask-openapi3', 
        'requests',
        'numpy',
        'pygeohash'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有必需依赖包已安装")
    return True

def check_files():
    """检查必需文件"""
    print("\n🔍 检查项目文件...")
    
    required_files = [
        'src/wsgi.py',
        'src/behavior recognition.py',
        'src/app/api/trajectory.py',
        'src/app/form/trajectory.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 缺少以下文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def start_server():
    """启动API服务器"""
    print("\n🚀 启动API服务器...")
    
    # 切换到src目录
    original_dir = os.getcwd()
    src_dir = os.path.join(original_dir, 'src')
    
    if not os.path.exists(src_dir):
        print("❌ src目录不存在")
        return False
    
    try:
        os.chdir(src_dir)
        print(f"📁 切换到目录: {src_dir}")
        
        # 启动服务
        print("🔄 正在启动Flask服务...")
        print("📡 服务地址: http://localhost:5000")
        print("📚 API文档: http://localhost:5000/openapi/swagger")
        print("\n按 Ctrl+C 停止服务\n")
        print("=" * 60)
        
        # 使用subprocess启动服务
        subprocess.run([sys.executable, 'wsgi.py'], check=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 服务启动失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
        return False
    finally:
        os.chdir(original_dir)

def show_usage_info():
    """显示使用信息"""
    print("\n" + "=" * 60)
    print("📖 API使用指南")
    print("=" * 60)
    print("服务启动后，您可以:")
    print("1. 访问 http://localhost:5000/openapi/swagger 查看API文档")
    print("2. 运行测试脚本:")
    print("   - python quick_test_api.py (快速测试)")
    print("   - python test_trajectory_api.py (完整测试)")
    print("   - python demo_api_client.py (客户端演示)")
    print("3. 使用curl或Postman等工具测试API")
    print("\n主要API端点:")
    print("- GET  /api/v1/trajectory/health (健康检查)")
    print("- POST /api/v1/trajectory/classify (航迹分类)")
    print("- GET  /api/v1/trajectory/database/info (数据库信息)")
    print("- POST /api/v1/trajectory/database/rebuild (重建数据库)")

def main():
    """主函数"""
    print("🌟 航迹行为识别API服务启动器")
    print("=" * 60)
    
    # 检查依赖和文件
    if not check_dependencies():
        return
    
    if not check_files():
        return
    
    show_usage_info()
    
    # 询问是否启动服务
    print("\n" + "=" * 60)
    response = input("是否立即启动API服务? (Y/n): ").strip().lower()
    
    if response in ['', 'y', 'yes']:
        start_server()
    else:
        print("👋 您可以稍后手动启动服务:")
        print("cd src && python wsgi.py")

if __name__ == "__main__":
    main()