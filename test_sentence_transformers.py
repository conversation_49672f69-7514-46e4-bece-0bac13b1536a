#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试sentence-transformers修复
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试各种向量化模块的导入"""
    print("🧪 测试向量化模块导入...")
    
    results = {}
    
    # 测试 sentence-transformers
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ sentence-transformers 导入成功")
        
        # 尝试创建一个轻量级模型
        print("   测试模型加载...")
        model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        print("   ✅ 模型加载成功")
        
        # 测试向量化
        test_text = "测试文本"
        vector = model.encode(test_text)
        print(f"   ✅ 文本向量化成功 (维度: {len(vector)})")
        
        results['sentence_transformers'] = True
    except Exception as e:
        print(f"❌ sentence-transformers 测试失败: {e}")
        results['sentence_transformers'] = False
    
    # 测试 transformers
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        print("✅ transformers 导入成功")
        results['transformers'] = True
    except Exception as e:
        print(f"❌ transformers 导入失败: {e}")
        results['transformers'] = False
    
    # 测试 llama-cpp-python
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python 导入成功")
        results['llama_cpp'] = True
    except Exception as e:
        print(f"❌ llama-cpp-python 导入失败: {e}")
        results['llama_cpp'] = False
    
    return results

def test_behavior_recognition():
    """测试行为识别模块"""
    print("\n🔍 测试行为识别模块...")
    
    try:
        # 导入行为识别模块
        import importlib.util
        behavior_file_path = os.path.join(os.path.dirname(__file__), "src", "behavior recognition.py")
        spec = importlib.util.spec_from_file_location("behavior_recognition", behavior_file_path)
        behavior_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(behavior_module)
        
        print("✅ 行为识别模块加载成功")
        
        # 检查可用的向量化方法
        print(f"   SENTENCE_TRANSFORMERS_AVAILABLE: {behavior_module.SENTENCE_TRANSFORMERS_AVAILABLE}")
        print(f"   TRANSFORMERS_AVAILABLE: {behavior_module.TRANSFORMERS_AVAILABLE}")
        print(f"   LLAMA_CPP_AVAILABLE: {behavior_module.LLAMA_CPP_AVAILABLE}")
        print(f"   FAISS_AVAILABLE: {behavior_module.FAISS_AVAILABLE}")
        
        return True
    except Exception as e:
        print(f"❌ 行为识别模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 sentence-transformers 修复验证")
    print("=" * 50)
    
    # 测试导入
    import_results = test_imports()
    
    # 测试行为识别模块
    behavior_success = test_behavior_recognition()
    
    # 总结
    print("\n📊 测试结果总结:")
    print("=" * 30)
    
    for module, success in import_results.items():
        status = "✅ 可用" if success else "❌ 不可用"
        print(f"  {module}: {status}")
    
    behavior_status = "✅ 正常" if behavior_success else "❌ 异常"
    print(f"  行为识别模块: {behavior_status}")
    
    if import_results.get('sentence_transformers', False):
        print("\n🎉 sentence-transformers 修复成功！")
        print("现在启动API服务器时应该不会再看到警告信息了。")
    else:
        print("\n⚠️ sentence-transformers 仍有问题，将使用备用方案。")
    
    print("\n💡 提示: 重新启动API服务器以应用修复:")
    print("   python start_api_server.py")

if __name__ == "__main__":
    main()