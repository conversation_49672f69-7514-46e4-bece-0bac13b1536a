#!/usr/bin/env python3
"""
使用 huggingface_hub 下载 Qwen3-Embedding GGUF 模型
"""

import os
from pathlib import Path

def download_with_huggingface_hub():
    """使用 huggingface_hub 下载模型"""
    try:
        from huggingface_hub import hf_hub_download, list_repo_files
        
        repo_id = "Qwen/Qwen3-Embedding-0.6B-GGUF"
        
        print(f"🔍 正在查看仓库文件列表: {repo_id}")
        
        # 列出仓库中的所有文件
        try:
            files = list_repo_files(repo_id)
            gguf_files = [f for f in files if f.endswith('.gguf')]
            
            print(f"📁 找到的 GGUF 文件:")
            for i, file in enumerate(gguf_files, 1):
                print(f"  {i}. {file}")
            
            if not gguf_files:
                print("❌ 仓库中没有找到 .gguf 文件")
                return None
            
            # 选择第一个 GGUF 文件下载
            selected_file = gguf_files[0]
            print(f"\n📥 正在下载: {selected_file}")
            
            # 创建本地目录
            local_dir = Path("models")
            local_dir.mkdir(exist_ok=True)
            
            # 下载文件
            downloaded_path = hf_hub_download(
                repo_id=repo_id,
                filename=selected_file,
                local_dir=str(local_dir),
                local_dir_use_symlinks=False
            )
            
            # 重命名为标准名称
            target_path = local_dir / "qwen3-embedding-0.6b.gguf"
            if downloaded_path != str(target_path):
                os.rename(downloaded_path, target_path)
            
            print(f"✅ 下载完成: {target_path}")
            return str(target_path)
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None
            
    except ImportError:
        print("❌ huggingface_hub 未安装")
        print("请运行: pip install huggingface_hub")
        return None

def manual_download_instructions():
    """显示手动下载说明"""
    print("\n📋 手动下载步骤:")
    print("1. 访问: https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF")
    print("2. 点击 'Files and versions' 标签")
    print("3. 找到并下载任意一个 .gguf 文件 (推荐较小的量化版本)")
    print("4. 将下载的文件重命名为: qwen3-embedding-0.6b.gguf")
    print("5. 放置到目录: models/")
    print("\n💡 提示:")
    print("- q4_0.gguf: 4位量化，文件较小，速度快")
    print("- q8_0.gguf: 8位量化，质量更好，文件较大")
    print("- f16.gguf: 16位浮点，质量最好，文件最大")

def verify_model_file():
    """验证模型文件"""
    model_path = Path("models/qwen3-embedding-0.6b.gguf")
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    file_size = model_path.stat().st_size
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"✅ 模型文件存在: {model_path}")
    print(f"📊 文件大小: {file_size_mb:.1f} MB")
    
    # 验证文件大小合理性（GGUF 嵌入模型通常在几十MB到几百MB）
    if file_size_mb < 10:
        print("⚠️ 文件大小异常小，可能不是有效的模型文件")
        return False
    elif file_size_mb > 2000:
        print("⚠️ 文件大小异常大，请确认是否为正确的模型")
    
    return True

def main():
    """主函数"""
    print("=== Qwen3-Embedding GGUF 模型下载 ===\n")
    
    # 检查是否已存在模型文件
    if verify_model_file():
        print("✅ 模型文件已存在，无需重新下载")
        return
    
    # 尝试自动下载
    print("🔄 尝试自动下载...")
    downloaded_path = download_with_huggingface_hub()
    
    if downloaded_path:
        print(f"\n🎉 模型下载成功!")
        print(f"📁 模型路径: {os.path.abspath(downloaded_path)}")
        
        # 验证下载的文件
        if verify_model_file():
            print("\n✅ 模型文件验证通过")
            print("\n📋 下一步:")
            print("1. 运行: python test_qwen_gguf.py")
            print("2. 在 behavior recognition.py 中设置: set_embedding_model_type('qwen')")
        else:
            print("\n⚠️ 模型文件验证失败")
    else:
        print("\n❌ 自动下载失败")
        manual_download_instructions()

if __name__ == "__main__":
    main()
