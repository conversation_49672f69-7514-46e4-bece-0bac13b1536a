[uwsgi]
# http 协议对客户端开发的端口号，客户端通过此端口访问 flask web 服务接口
http = 0.0.0.0:5000
# 使用真实IP
log-x-forwarded-for = true
# 应用目录，即python代码所在目录
pythonpath = /work/src
# web 应用python主程序
wsgi-file = /work/src/wsgi.py
# flask应用实例
callable = app
processes = $(PROCESSES)
threads = 2
enable-threads = true

pidfile = /tmp/uwsgi.pid
# 指定日志文件
;logto = /data/log/webapp.log
# 输出到控制台
log-master = true
harakiri = $(HTTP_TIMEOUT)
http-timeout = $(HTTP_TIMEOUT)
