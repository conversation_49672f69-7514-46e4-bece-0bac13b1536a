[program:webapp]
command = uwsgi /work/conf/uwsgi.ini
autostart = true
autorestart = true
# 将stderr的日志写入stdout日志文件中
redirect_stderr = true
stdout_logfile = /data/log/webapp.log
# 杀死进程组包括子进程
stopasgroup = true
killasgroup = true


[program:worker]
command = python -u /work/src/bin/worker.py
autostart = true
autorestart = true
# 将stderr的日志写入stdout日志文件中
redirect_stderr = true
stdout_logfile = /data/log/worker.log
# 杀死进程组包括子进程
stopasgroup = true
killasgroup = true