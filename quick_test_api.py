#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航迹行为识别API快速测试脚本
快速验证API接口是否正常工作
"""

import requests
import json

def test_api_quick():
    """快速测试所有API接口"""
    
    base_url = "http://localhost:5000"
    headers = {'Content-Type': 'application/json'}
    
    print("🚀 航迹行为识别API快速测试")
    print("=" * 50)
    
    # 1. 测试健康检查
    print("\n1️⃣ 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/trajectory/health", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查通过")
            status = data.get('data', {}).get('status', 'unknown')
            print(f"   系统状态: {status}")
        else:
            print(f"❌ 健康检查失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 2. 测试数据库信息
    print("\n2️⃣ 测试数据库信息...")
    try:
        response = requests.get(f"{base_url}/api/v1/trajectory/database/info", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 数据库信息查询成功")
            db_info = data.get('data', {}).get('database_info', {})
            print(f"   数据库类型: {db_info.get('database_type', 'unknown')}")
            print(f"   缓存数据库数: {db_info.get('cached_databases', 0)}")
        else:
            print(f"❌ 数据库信息查询失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 数据库信息查询异常: {e}")
    
    # 3. 测试航迹分类
    print("\n3️⃣ 测试航迹分类...")
    try:
        # 简单的测试航迹数据
        test_data = {
            "ship_name": "文森号航空母舰",
            "trajectory_coords": [
                [35.1234, 139.5678],
                [35.1244, 139.5688],
                [35.1254, 139.5698],
                [35.1264, 139.5708]
            ],
            "top_k": 3
        }
        
        response = requests.post(
            f"{base_url}/api/v1/trajectory/classify", 
            headers=headers, 
            json=test_data,
            timeout=60  # 分类可能需要更长时间
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('data', {})
            if result.get('success', False):
                print("✅ 航迹分类成功")
                print(f"   预测行为: {result.get('predicted_action', 'unknown')}")
                print(f"   置信度: {result.get('confidence', 0):.2%}")
                print(f"   处理时间: {result.get('processing_time_ms', 0)}ms")
            else:
                print(f"❌ 航迹分类失败: {result.get('error', 'unknown')}")
        else:
            print(f"❌ 航迹分类请求失败 (状态码: {response.status_code})")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('message', 'unknown error')}")
            except:
                print(f"   响应内容: {response.text[:200]}")
                
    except Exception as e:
        print(f"❌ 航迹分类异常: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 快速测试完成")
    print("\n💡 提示:")
    print("   - 如果所有测试都通过，API服务正常工作")
    print("   - 如果有测试失败，请检查:")
    print("     1. API服务器是否正在运行 (python wsgi.py)")
    print("     2. 依赖包是否已安装 (pip install -r requirements.txt)")
    print("     3. behavior recognition.py 文件是否存在")

if __name__ == "__main__":
    test_api_quick()