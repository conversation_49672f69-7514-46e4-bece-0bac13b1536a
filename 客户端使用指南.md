# 航迹行为识别API客户端使用指南

## 🎯 快速开始

### 1. 启动API服务器
```bash
python start_api_server.py
```

### 2. 选择合适的测试方式

#### ⚡ 快速验证（推荐首次使用）
```bash
python quick_client_test.py
```
- ✅ 耗时: < 1分钟
- ✅ 验证API连接和基础功能
- ✅ 智能跳过耗时的数据库构建

#### 🧪 完整测试（首次分类）
```bash
python demo_api_client.py
```
- ⏰ 耗时: 5-10分钟（首次需要构建数据库）
- ✅ 完整功能演示
- ✅ 性能基准测试

## 📋 测试工具对比

| 测试工具 | 用途 | 耗时 | 适用场景 |
|----------|------|------|----------|
| `quick_client_test.py` | 快速验证 | < 1分钟 | 日常检查、开发调试 |
| `demo_api_client.py` | 完整演示 | 5-10分钟 | 首次使用、功能展示 |
| `test_trajectory_api.py` | 详细测试 | 10-15分钟 | 深度测试、性能评估 |
| `quick_test_api.py` | 基础验证 | < 30秒 | 最快速的连接测试 |

## 🔄 典型使用流程

### 首次使用
```mermaid
graph TD
    A[启动服务器] --> B[快速验证]
    B --> C{基础功能正常?}
    C -->|是| D[运行完整测试]
    C -->|否| E[检查服务器和依赖]
    D --> F[构建向量数据库]
    F --> G[后续使用快速测试]
    E --> A
```

### 日常使用
```mermaid
graph TD
    A[启动服务器] --> B[快速验证]
    B --> C{需要详细测试?}
    C -->|是| D[运行完整测试]
    C -->|否| E[完成]
    D --> E
```

## 🛠️ 故障排除

### 问题1: 连接失败
```
❌ 无法连接到API服务器
```

**解决方案**:
1. 确认服务器正在运行: `python start_api_server.py`
2. 检查端口5000是否被占用
3. 确认防火墙设置

### 问题2: 首次分类超时
```
❌ 请求超时 (首次分类)
```

**解决方案**:
1. ✅ **已修复**: 客户端现在使用5分钟超时
2. 耐心等待数据库构建完成
3. 使用快速测试跳过首次分类

### 问题3: 响应格式异常
```
❌ AttributeError: 'list' object has no attribute 'get'
```

**解决方案**:
1. ✅ **已修复**: 现在安全处理所有响应格式
2. 检查API服务器版本是否最新
3. 查看服务器日志了解详细错误

## 📊 性能预期

### 首次使用（需要构建数据库）
| 操作 | 预期时间 | 说明 |
|------|----------|------|
| 健康检查 | < 1秒 | 基础连接测试 |
| 数据库信息 | < 1秒 | 查询缓存状态 |
| **首次分类** | **1-5分钟** | **构建向量数据库** |
| 错误处理 | < 5秒 | 验证输入校验 |

### 后续使用（数据库已存在）
| 操作 | 预期时间 | 说明 |
|------|----------|------|
| 健康检查 | < 1秒 | 基础连接测试 |
| 数据库信息 | < 1秒 | 查询缓存状态 |
| **航迹分类** | **5-30秒** | **使用缓存数据库** |
| 性能测试 | 1-2分钟 | 连续多次分类 |

## 🎯 最佳实践

### 1. 开发环境
```bash
# 每日开发流程
python start_api_server.py    # 启动服务器
python quick_client_test.py   # 快速验证
# 开始开发工作...
```

### 2. 演示环境
```bash
# 准备演示
python start_api_server.py      # 启动服务器
python demo_api_client.py       # 完整演示（首次）
# 演示过程中使用快速测试...
python quick_client_test.py     # 快速验证
```

### 3. 生产环境
```bash
# 部署验证
python start_api_server.py      # 启动服务器
python test_trajectory_api.py   # 详细测试
# 监控和维护...
```

## 🔧 自定义配置

### 修改服务器地址
```python
# 在客户端代码中修改
api = TrajectoryAPI(base_url="http://your-server:5000")
```

### 调整超时时间
```python
# 根据网络环境调整
timeout=300  # 5分钟（首次分类）
timeout=60   # 1分钟（后续分类）
timeout=10   # 10秒（基础查询）
```

### 自定义测试数据
```python
# 创建自定义航迹
custom_trajectory = create_custom_trajectory(
    start_lat=35.0,
    start_lng=139.0,
    movement_type="circular",  # linear, circular, stationary
    points=8
)
```

## 📈 性能优化建议

### 1. 向量化模型选择
- **最佳**: Qwen3-Embedding（如果模型文件存在）
- **推荐**: sentence-transformers（轻量级，现已可用）
- **备选**: transformers（完整功能）
- **兜底**: 哈希方法（最快速度）

### 2. 数据库缓存策略
- 首次使用后，向量数据库会自动缓存
- 后续相同舰船的查询会复用缓存
- 定期清理不需要的缓存数据库

### 3. 请求优化
- 批量测试时添加适当延迟
- 避免并发过多的分类请求
- 监控服务器资源使用情况

## 🎉 成功指标

### ✅ 系统健康
- 健康检查返回 "healthy"
- 所有必要模型状态为 "available"
- 数据库连接正常

### ✅ 功能正常
- 航迹分类返回合理结果
- 置信度在0-1范围内
- 响应时间在预期范围内

### ✅ 用户体验
- 清晰的进度提示
- 合理的超时设置
- 友好的错误信息

---

现在您拥有了完整的客户端测试工具集，可以根据不同需求选择合适的测试方式。祝您使用愉快！🚀