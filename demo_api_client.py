#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航迹行为识别API客户端演示
基于项目自带的TrajectoryAPI类进行测试演示
"""

import sys
import os
import requests
import json
import time

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入项目自带的API客户端
from app.api.trajectory_usage_example import TrajectoryAPI, create_custom_trajectory

def demo_with_project_client():
    """使用项目自带的客户端进行演示"""
    
    print("🚀 航迹行为识别API客户端演示")
    print("使用项目自带的 TrajectoryAPI 类")
    print("=" * 60)
    
    # 初始化API客户端
    api = TrajectoryAPI(base_url="http://localhost:5000")
    
    # 1. 系统健康检查
    print("\n1️⃣ 系统健康检查")
    print("-" * 30)
    
    health_result = api.health_check()
    if 'error' not in health_result:
        print("✅ 健康检查成功")
        data = health_result.get('data', {})
        print(f"   系统状态: {data.get('status', 'unknown')}")
        print(f"   版本: {data.get('version', 'unknown')}")
        
        model_status = data.get('model_status', {})
        print("   模型状态:")
        for model, status in model_status.items():
            icon = "✅" if status == "available" else "❌"
            print(f"     {icon} {model}: {status}")
    else:
        print(f"❌ 健康检查失败: {health_result['error']}")
        return
    
    # 2. 数据库信息查询
    print("\n2️⃣ 数据库信息查询")
    print("-" * 30)
    
    db_info = api.get_database_info()
    if 'error' not in db_info:
        print("✅ 数据库信息查询成功")
        data = db_info.get('data', {})
        database_info = data.get('database_info', {})
        
        print(f"   数据库类型: {database_info.get('database_type', 'unknown')}")
        print(f"   缓存数据库数: {database_info.get('cached_databases', 0)}")
        print(f"   支持的行为: {database_info.get('supported_actions', [])}")
        
        ships = data.get('ships', [])
        print(f"   已缓存的舰船: {ships if ships else '无'}")
    else:
        print(f"❌ 数据库信息查询失败: {db_info['error']}")
    
    # 3. 航迹分类测试
    print("\n3️⃣ 航迹分类测试")
    print("-" * 30)
    
    test_cases = [
        {
            "name": "线性航迹 (航渡行为)",
            "ship_name": "文森号航空母舰",
            "trajectory": create_custom_trajectory(35.1234, 139.5678, "linear", 8),
            "top_k": 5
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   测试 3.{i}: {test_case['name']}")
        print(f"   舰船: {test_case['ship_name']}")
        print(f"   坐标点数: {len(test_case['trajectory'])}")
        print(f"   前3个坐标: {test_case['trajectory'][:3]}")
        
        # 执行分类 - 增加超时时间，因为首次分类需要构建向量数据库
        print(f"   ⚠️ 首次分类可能需要1-5分钟（构建向量数据库）...")
        start_time = time.time()
        
        # 修改TrajectoryAPI的超时时间
        original_timeout = 30
        try:
            # 临时修改超时时间为5分钟
            import requests
            result = requests.post(
                f"{api.base_url}{api.api_prefix}/classify",
                json={
                    "ship_name": test_case['ship_name'],
                    "trajectory_coords": test_case['trajectory'],
                    "top_k": test_case['top_k']
                },
                headers=api.headers,
                timeout=300  # 5分钟超时
            ).json()
        except requests.exceptions.RequestException as e:
            result = {"error": f"请求失败: {str(e)}"}
        
        elapsed_time = int((time.time() - start_time) * 1000)
        
        # 安全地处理响应数据
        if 'error' not in result and isinstance(result, dict):
            # 处理Flask响应格式 {"code": 0, "message": "ok", "data": {...}}
            if 'data' in result:
                data = result.get('data', {})
            else:
                # 直接的响应格式
                data = result
                
            if isinstance(data, dict) and data.get('success', False):
                print(f"   ✅ 分类成功 (总耗时: {elapsed_time}ms)")
                print(f"      预测行为: {data.get('predicted_action', 'unknown')}")
                print(f"      置信度: {data.get('confidence', 0):.2%}")
                print(f"      处理时间: {data.get('processing_time_ms', 0)}ms")
                
                # 显示相似样本
                similar_samples = data.get('similar_samples', [])
                if similar_samples and isinstance(similar_samples, list):
                    print(f"      相似样本:")
                    for j, sample in enumerate(similar_samples[:2], 1):  # 只显示前2个
                        if isinstance(sample, dict):
                            print(f"        {j}. {sample.get('action', 'unknown')} (相似度: {sample.get('similarity', 0):.2%})")
            else:
                print(f"   ❌ 分类失败: {data.get('error', 'unknown') if isinstance(data, dict) else 'response format error'}")
        else:
            if isinstance(result, dict) and 'error' in result:
                print(f"   ❌ 请求失败: {result['error']}")
            else:
                print(f"   ❌ 请求失败: 响应格式异常")
        
        # 添加延迟避免请求过快
        if i < len(test_cases):
            time.sleep(1)
    
    # 4. 错误处理测试
    print("\n4️⃣ 错误处理测试")
    print("-" * 30)
    
    # 测试无效坐标
    print("   测试无效坐标数据...")
    invalid_coords = [[200, 300], [35.1234, 139.5678]]  # 超出范围的坐标
    
    result = api.classify_trajectory("测试舰船", invalid_coords)
    
    # 安全地处理结果
    if isinstance(result, dict):
        if 'error' in result:
            print("   ✅ 正确捕获了无效坐标错误")
            print(f"      错误信息: {result['error']}")
        else:
            # 处理Flask响应格式
            if 'data' in result:
                data = result.get('data', {})
            else:
                data = result
                
            if isinstance(data, dict):
                if not data.get('success', True):
                    print("   ✅ 服务器正确拒绝了无效坐标")
                    print(f"      错误信息: {data.get('error', 'unknown')}")
                else:
                    print("   ⚠️ 无效坐标未被正确处理")
            else:
                print("   ⚠️ 响应格式异常")
    elif isinstance(result, list):
        print("   ⚠️ 服务器返回了列表格式的响应，可能是错误信息")
        print(f"      响应内容: {result}")
    else:
        print("   ⚠️ 未知的响应格式")
    
    # 5. 性能测试
    print("\n5️⃣ 性能测试")
    print("-" * 30)
    
    print("   测试连续分类性能...")
    print("   ⚠️ 由于向量数据库已构建，后续请求应该更快...")
    ship_name = "文森号航空母舰"
    test_trajectory = create_custom_trajectory(35.0, 139.0, "linear", 6)
    
    times = []
    for i in range(3):
        start_time = time.time()
        
        # 使用较短的超时时间，因为数据库已经构建
        try:
            result = requests.post(
                f"{api.base_url}{api.api_prefix}/classify",
                json={
                    "ship_name": ship_name,
                    "trajectory_coords": test_trajectory,
                    "top_k": 3
                },
                headers=api.headers,
                timeout=60  # 1分钟超时
            ).json()
        except requests.exceptions.RequestException as e:
            result = {"error": f"请求失败: {str(e)}"}
            
        elapsed_time = int((time.time() - start_time) * 1000)
        times.append(elapsed_time)
        
        # 安全地处理结果
        if isinstance(result, dict):
            if 'error' not in result:
                # 处理Flask响应格式
                if 'data' in result:
                    data = result.get('data', {})
                else:
                    data = result
                    
                if isinstance(data, dict) and data.get('success', False):
                    print(f"   第{i+1}次: {elapsed_time}ms - {data.get('predicted_action', 'unknown')}")
                else:
                    print(f"   第{i+1}次: {elapsed_time}ms - 分类失败")
            else:
                print(f"   第{i+1}次: {elapsed_time}ms - 请求失败: {result['error']}")
        else:
            print(f"   第{i+1}次: {elapsed_time}ms - 响应格式异常")
        
        time.sleep(0.5)  # 短暂延迟
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"   平均响应时间: {avg_time:.0f}ms")
        print(f"   最快响应时间: {min(times)}ms")
        print(f"   最慢响应时间: {max(times)}ms")
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 API客户端演示完成!")
    print("\n💡 使用建议:")
    print("   1. 首次分类可能较慢，需要构建向量数据库")
    print("   2. 后续分类会使用缓存，速度明显提升")
    print("   3. 推荐使用4-20个坐标点代表12小时航迹")
    print("   4. 支持的行为类型: 编队、航渡、停靠、巡航")
    print("   5. 坐标格式: [[纬度, 经度], [纬度, 经度], ...]")

def interactive_test():
    """交互式测试"""
    print("\n" + "=" * 60)
    print("🎮 交互式测试模式")
    print("=" * 60)
    
    api = TrajectoryAPI(base_url="http://localhost:5000")
    
    while True:
        print("\n请选择测试项目:")
        print("1. 健康检查")
        print("2. 数据库信息")
        print("3. 航迹分类 (自定义)")
        print("4. 航迹分类 (预设)")
        print("5. 数据库重建")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-5): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            result = api.health_check()
            print("\n健康检查结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        elif choice == '2':
            result = api.get_database_info()
            print("\n数据库信息:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        elif choice == '3':
            ship_name = input("请输入舰船名称: ").strip()
            if not ship_name:
                print("❌ 舰船名称不能为空")
                continue
            
            print("请输入坐标点 (格式: 纬度,经度)，输入空行结束:")
            coords = []
            while True:
                coord_str = input(f"坐标点 {len(coords)+1}: ").strip()
                if not coord_str:
                    break
                try:
                    lat, lng = map(float, coord_str.split(','))
                    coords.append([lat, lng])
                except:
                    print("❌ 格式错误，请使用 '纬度,经度' 格式")
            
            if coords:
                result = api.classify_trajectory(ship_name, coords)
                print("\n分类结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
            else:
                print("❌ 没有输入坐标点")
        elif choice == '4':
            ship_name = input("请输入舰船名称 (默认: 文森号航空母舰): ").strip()
            if not ship_name:
                ship_name = "文森号航空母舰"
            
            print("请选择预设航迹:")
            print("1. 线性航迹 (航渡)")
            print("2. 圆形航迹 (巡航)")
            print("3. 静止航迹 (停靠)")
            
            preset_choice = input("选择 (1-3): ").strip()
            
            if preset_choice == '1':
                coords = create_custom_trajectory(35.0, 139.0, "linear", 8)
            elif preset_choice == '2':
                coords = create_custom_trajectory(36.0, 140.0, "circular", 8)
            elif preset_choice == '3':
                coords = create_custom_trajectory(36.8485, -76.2951, "stationary", 6)
            else:
                print("❌ 无效选择")
                continue
            
            print(f"使用预设航迹，坐标点数: {len(coords)}")
            result = api.classify_trajectory(ship_name, coords)
            print("\n分类结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        elif choice == '5':
            ship_name = input("请输入要重建数据库的舰船名称: ").strip()
            if not ship_name:
                print("❌ 舰船名称不能为空")
                continue
            
            print(f"⚠️ 重建 {ship_name} 的数据库可能需要几分钟时间...")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm in ['y', 'yes']:
                result = api.rebuild_database(ship_name)
                print("\n重建结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
            else:
                print("已取消重建操作")
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("选择运行模式:")
    print("1. 完整演示")
    print("2. 交互式测试")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == '1':
        demo_with_project_client()
    elif choice == '2':
        demo_with_project_client()  # 先运行演示
        interactive_test()  # 再进入交互模式
    else:
        print("使用默认演示模式...")
        demo_with_project_client()

if __name__ == "__main__":
    main()