#!/usr/bin/env python3
"""
航迹行为识别API测试客户端
"""

import requests
import json
import time

class TrajectoryAPITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.api_prefix = "/api/v1/trajectory"
        self.headers = {
            'Content-Type': 'application/json'
        }
    
    def test_health_check(self):
        """测试健康检查接口"""
        print("🔍 测试1：健康检查接口")
        print("-" * 50)
        
        url = f"{self.base_url}{self.api_prefix}/health"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            print(f"请求URL: {url}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 健康检查成功")
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                return True
            else:
                print(f"❌ 健康检查失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def test_trajectory_classification(self):
        """测试航迹分类接口"""
        print("\n🎯 测试2：航迹分类接口")
        print("-" * 50)
        
        url = f"{self.base_url}{self.api_prefix}/classify"
        
        # 测试数据：巡航模式
        payload = {
            "ship_name": "文森号航空母舰",
            "trajectory_coords": [
                [35.1234, 139.5678],
                [35.1244, 139.5688],
                [35.1254, 139.5698],
                [35.1264, 139.5708],
                [35.1274, 139.5718]
            ],
            "top_k": 3
        }
        
        try:
            print(f"请求URL: {url}")
            print("请求数据:")
            print(json.dumps(payload, ensure_ascii=False, indent=2))
            print("\n正在处理... (首次运行可能需要1-2分钟构建数据库)")
            
            start_time = time.time()
            response = requests.post(url, json=payload, headers=self.headers, timeout=180)  # 3分钟超时
            end_time = time.time()
            
            print(f"状态码: {response.status_code}")
            print(f"响应时间: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 航迹分类成功")
                
                if data.get('code') == 0:
                    result = data.get('data', {})
                    print(f"\n🎯 分类结果:")
                    print(f"  预测行为: {result.get('predicted_action', 'Unknown')}")
                    print(f"  置信度: {result.get('confidence', 0):.1%}")
                    print(f"  处理时间: {result.get('processing_time_ms', 0)}ms")
                    
                    if 'action_scores' in result:
                        print(f"\n📊 各行为得分:")
                        for action, scores in result['action_scores'].items():
                            print(f"    {action}: {scores.get('combined_score', 0):.1%}")
                else:
                    print(f"❌ API返回错误: {data.get('message', 'Unknown error')}")
                
                print("\n完整响应:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                return True
            else:
                print(f"❌ 航迹分类失败: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时，可能是首次运行需要更长时间")
            return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def test_database_info(self):
        """测试数据库信息接口"""
        print("\n📊 测试3：数据库信息接口")
        print("-" * 50)
        
        url = f"{self.base_url}{self.api_prefix}/database/info"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            print(f"请求URL: {url}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 数据库信息获取成功")
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                return True
            else:
                print(f"❌ 获取数据库信息失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def test_different_trajectory_types(self):
        """测试不同类型的航迹"""
        print("\n🧪 测试4：不同航迹类型分类")
        print("-" * 50)
        
        test_cases = [
            {
                "name": "停靠模式（位置固定）",
                "ship_name": "文森号航空母舰",
                "trajectory_coords": [
                    [36.8485, -76.2951],  # 诺福克海军基地
                    [36.8485, -76.2951],
                    [36.8486, -76.2952],
                    [36.8485, -76.2951],
                    [36.8484, -76.2950]
                ]
            },
            {
                "name": "航渡模式（长距离移动）",
                "ship_name": "文森号航空母舰", 
                "trajectory_coords": [
                    [35.0000, 139.0000],
                    [35.5000, 139.5000],
                    [36.0000, 140.0000],
                    [36.5000, 140.5000],
                    [37.0000, 141.0000]
                ]
            }
        ]
        
        url = f"{self.base_url}{self.api_prefix}/classify"
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case['name']}")
            
            payload = {
                "ship_name": test_case["ship_name"],
                "trajectory_coords": test_case["trajectory_coords"],
                "top_k": 3
            }
            
            try:
                response = requests.post(url, json=payload, headers=self.headers, timeout=60)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('code') == 0:
                        result = data.get('data', {})
                        print(f"  ✅ 预测结果: {result.get('predicted_action', 'Unknown')} (置信度: {result.get('confidence', 0):.1%})")
                    else:
                        print(f"  ❌ 分类失败: {data.get('message', 'Unknown')}")
                else:
                    print(f"  ❌ 请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 请求异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 航迹行为识别API测试套件")
        print("=" * 60)
        
        # 测试1：健康检查
        health_ok = self.test_health_check()
        
        if not health_ok:
            print("\n❌ 健康检查失败，请确认服务是否正常启动")
            return False
        
        # 测试2：基本分类功能
        classify_ok = self.test_trajectory_classification()
        
        # 测试3：数据库信息
        self.test_database_info()
        
        # 测试4：不同类型航迹
        if classify_ok:
            self.test_different_trajectory_types()
        
        print("\n🎉 API测试完成！")
        return True

def main():
    """主函数"""
    tester = TrajectoryAPITester()
    
    print("开始测试前，请确认：")
    print("1. Flask应用已启动 (python src/wsgi.py)")
    print("2. 服务运行在 http://localhost:5000")
    print("3. 网络连接正常（需要访问API获取历史数据）")
    
    input("\n按Enter键开始测试...")
    
    tester.run_all_tests()

if __name__ == "__main__":
    main()