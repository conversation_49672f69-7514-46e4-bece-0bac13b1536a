#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证服务器能否正常启动（不实际启动服务）
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def verify_startup():
    """验证所有模块能否正常导入和初始化"""
    print("🔍 验证服务器启动准备...")
    
    try:
        # 测试核心模块导入
        print("1. 测试核心模块导入...")
        from app.form.trajectory import (
            TrajectoryClassificationBody, 
            DatabaseRebuildBody
        )
        print("   ✅ 表单模型导入成功")
        
        # 测试API模块导入
        print("2. 测试API模块导入...")
        from app.api.trajectory import api
        print("   ✅ API模块导入成功")
        
        # 测试创建各种请求模型
        print("3. 测试请求模型创建...")
        
        # 分类请求
        classify_request = TrajectoryClassificationBody(
            ship_name="测试舰船",
            trajectory_coords=[[35.0, 139.0], [35.1, 139.1]],
            top_k=5
        )
        print("   ✅ 分类请求模型创建成功")
        
        # 重建请求
        rebuild_request = DatabaseRebuildBody(ship_name="测试舰船")
        print("   ✅ 重建请求模型创建成功")
        
        # 测试行为识别模块
        print("4. 测试行为识别模块...")
        import importlib.util
        behavior_file_path = os.path.join(os.path.dirname(__file__), "src", "behavior recognition.py")
        spec = importlib.util.spec_from_file_location("behavior_recognition", behavior_file_path)
        behavior_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(behavior_module)
        print("   ✅ 行为识别模块加载成功")
        
        print("\n🎉 所有验证都通过了！")
        print("🚀 服务器准备就绪，可以正常启动！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 API服务器启动验证工具")
    print("=" * 50)
    
    success = verify_startup()
    
    if success:
        print("\n✅ 验证通过！现在可以启动API服务器了：")
        print("   python start_api_server.py")
        print("\n📚 或者直接启动：")
        print("   cd src && python wsgi.py")
    else:
        print("\n❌ 验证失败，请检查错误信息")