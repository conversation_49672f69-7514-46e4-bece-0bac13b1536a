#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的响应格式
对比优化前后的响应结构
"""

import requests
import json

def test_new_format():
    """测试新的响应格式"""
    
    print("🧪 测试优化后的响应格式")
    print("=" * 50)
    
    # 检查服务器连接
    try:
        health_response = requests.get("http://localhost:5000/api/v1/trajectory/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 服务器未运行，请先启动: python start_api_server.py")
            return
    except:
        print("❌ 无法连接到服务器，请先启动: python start_api_server.py")
        return
    
    print("✅ 服务器连接正常")
    
    # 测试数据（文森号真实巡航数据）
    test_data = {
        "ship_name": "文森号航空母舰",
        "trajectory_coords": [
            [21.17690333, -157.889655],
            [21.82, -156.8068],
            [22.7225, -154.4709],
            [28.8555, -136.7788],
            [31.17649667, -127.057945]
        ],
        "top_k": 5
    }
    
    print(f"\n📡 发送分类请求...")
    print(f"舰船: {test_data['ship_name']}")
    print(f"坐标点数: {len(test_data['trajectory_coords'])}")
    
    try:
        response = requests.post(
            "http://localhost:5000/api/v1/trajectory/classify",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ 请求成功")
            
            # 分析新的响应格式
            print(f"\n📊 新的响应格式分析:")
            print("=" * 30)
            
            data = result.get('data', {})
            
            # 1. 分类结果
            if 'classification' in data:
                classification = data['classification']
                print(f"🎯 分类结果:")
                print(f"   预测行为: {classification.get('predicted_action', 'unknown')}")
                print(f"   置信度: {classification.get('confidence', 0):.3f}")
                print(f"   置信度等级: {classification.get('confidence_level', 'unknown')}")
            
            # 2. 处理信息
            if 'processing' in data:
                processing = data['processing']
                print(f"\n⚙️ 处理信息:")
                print(f"   舰船名称: {processing.get('ship_name', 'unknown')}")
                print(f"   坐标点数: {processing.get('coordinate_count', 0)}")
                print(f"   处理时间: {processing.get('processing_time_ms', 0)}ms")
                print(f"   向量维度: {processing.get('vector_dimension', 0)}")
            
            # 3. 详细分析
            if 'analysis' in data:
                analysis = data['analysis']
                print(f"\n🔍 详细分析:")
                
                if 'action_scores' in analysis:
                    print(f"   行为得分:")
                    for action, scores in analysis['action_scores'].items():
                        print(f"     {action}:")
                        print(f"       投票数: {scores.get('vote_count', 0)}")
                        print(f"       投票比例: {scores.get('vote_ratio', 0):.1%}")
                        print(f"       平均相似度: {scores.get('avg_similarity', 0):.3f}")
                        print(f"       综合得分: {scores.get('combined_score', 0):.3f}")
                
                if 'similar_samples' in analysis:
                    samples = analysis['similar_samples']
                    print(f"   相似样本: {len(samples)} 个")
                    for i, sample in enumerate(samples[:2], 1):  # 只显示前2个
                        print(f"     {i}. ID:{sample.get('sample_id', 0)} - {sample.get('action', 'unknown')} (相似度: {sample.get('similarity', 0):.3f})")
            
            # 4. 总结
            if 'summary' in data:
                print(f"\n📝 总结:")
                print(f"   {data['summary']}")
            
            # 格式对比
            print(f"\n🎨 格式优化效果:")
            print("=" * 30)
            print("✅ 结构清晰 - 信息分层组织")
            print("✅ 数据精度 - 合理的小数位数")
            print("✅ 直观描述 - 置信度等级化")
            print("✅ 快速理解 - 一句话总结")
            print("✅ 易于解析 - 逻辑分组")
            
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时 - 首次分类需要构建数据库，请耐心等待")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def show_format_comparison():
    """显示格式对比"""
    
    print(f"\n📋 响应格式对比")
    print("=" * 50)
    
    print("🔴 优化前 (混乱):")
    print("```")
    print("{")
    print('  "success": true,')
    print('  "ship_name": "文森号航空母舰",')
    print('  "predicted_action": "巡航",')
    print('  "confidence": 0.47374242323063914,')
    print('  "coordinate_count": 5,')
    print('  "processing_time_ms": 238,')
    print('  "explanation": "🎯 **预测行为**: 巡航\\n🔥 **置信度**: 47.4%\\n...",')
    print('  "action_scores": {...},')
    print('  "similar_samples": [...]')
    print("}")
    print("```")
    
    print("\n🟢 优化后 (清晰):")
    print("```")
    print("{")
    print('  "success": true,')
    print('  "classification": {')
    print('    "predicted_action": "巡航",')
    print('    "confidence": 0.474,')
    print('    "confidence_level": "低"')
    print('  },')
    print('  "processing": {')
    print('    "ship_name": "文森号航空母舰",')
    print('    "coordinate_count": 5,')
    print('    "processing_time_ms": 238,')
    print('    "vector_dimension": 1024')
    print('  },')
    print('  "analysis": {')
    print('    "action_scores": {...},')
    print('    "similar_samples": [...]')
    print('  },')
    print('  "summary": "识别为巡航行为，置信度47.4%(低)，基于100%的历史样本匹配"')
    print("}")
    print("```")

def main():
    """主函数"""
    show_format_comparison()
    test_new_format()
    
    print(f"\n🎉 响应格式优化完成!")
    print("💡 优势:")
    print("   - 结构更清晰，便于理解和解析")
    print("   - 数据分组合理，逻辑性强")
    print("   - 去除冗余信息，提升可读性")
    print("   - 添加置信度等级和总结")

if __name__ == "__main__":
    main()