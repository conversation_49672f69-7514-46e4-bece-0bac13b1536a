# 航迹行为识别API测试使用指南

## 🎯 快速开始

### 1. 启动API服务

```bash
# 方法1: 使用启动脚本 (推荐)
python start_api_server.py

# 方法2: 手动启动
cd src
python wsgi.py
```

服务启动后可访问:
- **API服务**: http://localhost:5000
- **API文档**: http://localhost:5000/openapi/swagger

### 2. 快速验证API

```bash
# 运行快速测试 (30秒内完成)
python quick_test_api.py
```

### 3. 完整功能测试

```bash
# 运行完整测试 (包含多种测试用例)
python test_trajectory_api.py
```

### 4. 客户端演示

```bash
# 使用项目自带的客户端类进行演示
python demo_api_client.py
```

## 📁 测试文件说明

| 文件名 | 功能 | 特点 |
|--------|------|------|
| `start_api_server.py` | 启动API服务 | 自动检查依赖和文件 |
| `quick_test_api.py` | 快速API验证 | 3个核心接口，快速完成 |
| `test_trajectory_api.py` | 完整API测试 | 详细测试用例，交互式选项 |
| `demo_api_client.py` | 客户端演示 | 使用项目自带的客户端类 |
| `API测试说明.md` | 详细说明文档 | API接口文档和故障排除 |

## 🔧 API接口概览

### 核心接口

1. **健康检查** `GET /api/v1/trajectory/health`
   - 检查系统状态和模型可用性
   - 无需参数，快速响应

2. **航迹分类** `POST /api/v1/trajectory/classify` ⭐
   - 核心功能：分类舰船行为
   - 输入：舰船名称 + 坐标列表
   - 输出：预测行为 + 置信度

3. **数据库信息** `GET /api/v1/trajectory/database/info`
   - 查看向量数据库统计
   - 了解系统缓存状态

4. **数据库重建** `POST /api/v1/trajectory/database/rebuild`
   - 重建指定舰船的数据库
   - 管理员功能，耗时较长

## 🧪 测试用例示例

### 线性航迹 (航渡行为)
```json
{
  "ship_name": "文森号航空母舰",
  "trajectory_coords": [
    [35.1234, 139.5678],
    [35.1244, 139.5688],
    [35.1254, 139.5698],
    [35.1264, 139.5708]
  ],
  "top_k": 5
}
```

### 静止航迹 (停靠行为)
```json
{
  "ship_name": "辽宁舰",
  "trajectory_coords": [
    [36.8485, -76.2951],
    [36.8485, -76.2951],
    [36.8486, -76.2950],
    [36.8485, -76.2951]
  ],
  "top_k": 3
}
```

## ⚡ 测试流程建议

### 首次测试
1. **启动服务**: `python start_api_server.py`
2. **快速验证**: `python quick_test_api.py`
3. **查看文档**: 访问 http://localhost:5000/openapi/swagger

### 深入测试
1. **完整测试**: `python test_trajectory_api.py`
2. **客户端演示**: `python demo_api_client.py`
3. **自定义测试**: 使用curl或Postman

### 开发调试
1. **检查日志**: 观察服务器控制台输出
2. **健康检查**: 确认所有模块状态正常
3. **逐步测试**: 从简单到复杂的测试用例

## 🚨 常见问题解决

### 服务无法启动
```bash
# 检查依赖
pip install -r requirements.txt

# 检查端口占用
netstat -an | grep 5000

# 手动启动查看错误
cd src && python wsgi.py
```

### 分类失败
```bash
# 检查健康状态
curl http://localhost:5000/api/v1/trajectory/health

# 验证坐标格式
# 纬度: -90 到 90
# 经度: -180 到 180
```

### 首次分类很慢
- **正常现象**: 首次需要构建向量数据库
- **预期时间**: 1-5分钟
- **后续分类**: 几秒内完成

### 模型不可用
```bash
# 检查文件存在
ls -la "src/behavior recognition.py"

# 检查可选依赖
pip install faiss-cpu sentence-transformers transformers
```

## 📊 性能参考

| 操作 | 首次耗时 | 后续耗时 | 说明 |
|------|----------|----------|------|
| 健康检查 | < 1秒 | < 1秒 | 无需计算 |
| 数据库信息 | < 1秒 | < 1秒 | 读取缓存 |
| 航迹分类 | 1-5分钟 | 2-10秒 | 首次需构建数据库 |
| 数据库重建 | 2-10分钟 | 2-10分钟 | 重新获取历史数据 |

## 🎯 测试成功标准

### 基础功能
- ✅ 健康检查返回 `status: "healthy"`
- ✅ 数据库信息查询成功
- ✅ 航迹分类返回预测结果
- ✅ 置信度在合理范围 (0-1)

### 高级功能
- ✅ 支持多种舰船名称
- ✅ 处理不同类型的航迹模式
- ✅ 相似样本匹配合理
- ✅ 错误处理正确

### 性能要求
- ✅ 健康检查 < 1秒
- ✅ 后续分类 < 30秒
- ✅ 并发请求正常处理
- ✅ 内存使用稳定

## 🔍 调试技巧

### 查看详细日志
```bash
# 启动时观察控制台输出
cd src && python wsgi.py

# 关注这些关键信息:
# - 模块加载状态
# - 数据库构建进度
# - 向量化过程
# - 错误堆栈信息
```

### API响应分析
```bash
# 使用curl查看详细响应
curl -v -X GET http://localhost:5000/api/v1/trajectory/health

# 关注响应头和状态码
# 分析返回的JSON结构
```

### 逐步排查
1. **服务启动** → 检查依赖和文件
2. **健康检查** → 确认基础功能
3. **简单分类** → 验证核心逻辑
4. **复杂场景** → 测试边界情况

## 💡 最佳实践

### 测试数据准备
- 使用真实的经纬度坐标
- 坐标点数量建议 4-20 个
- 模拟不同的航行模式
- 包含边界情况测试

### 性能优化
- 首次使用预热系统
- 缓存常用舰船数据
- 批量测试时添加延迟
- 监控内存和CPU使用

### 错误处理
- 检查输入数据格式
- 验证坐标范围有效性
- 处理网络超时情况
- 记录详细错误信息

---

🎉 **恭喜！** 您现在已经掌握了航迹行为识别API的完整测试方法。

如有问题，请检查：
1. 项目文件是否完整
2. Python环境和依赖是否正确
3. API服务是否正常启动
4. 测试数据格式是否正确