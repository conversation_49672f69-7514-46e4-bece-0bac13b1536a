#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重启API服务器脚本
用于应用文档更新
"""

import os
import sys
import subprocess
import time
import signal
import psutil

def find_flask_process():
    """查找运行中的Flask进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            if 'wsgi.py' in cmdline or ('python' in cmdline and 'flask' in cmdline.lower()):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def stop_server():
    """停止现有的服务器"""
    print("🔍 查找运行中的服务器...")
    
    pid = find_flask_process()
    if pid:
        try:
            print(f"📍 找到服务器进程 PID: {pid}")
            print("🛑 正在停止服务器...")
            os.kill(pid, signal.SIGTERM)
            time.sleep(2)
            print("✅ 服务器已停止")
            return True
        except Exception as e:
            print(f"⚠️ 停止服务器时出错: {e}")
            return False
    else:
        print("ℹ️ 没有找到运行中的服务器")
        return True

def start_server():
    """启动服务器"""
    print("🚀 启动更新后的服务器...")
    
    # 切换到src目录
    original_dir = os.getcwd()
    src_dir = os.path.join(original_dir, 'src')
    
    try:
        os.chdir(src_dir)
        print(f"📁 切换到目录: {src_dir}")
        
        # 启动服务
        print("🔄 正在启动Flask服务...")
        print("📡 服务地址: http://localhost:5000")
        print("📚 API文档: http://localhost:5000/openapi/swagger")
        print("\n🎉 文档已更新！现在包含详细的参数说明")
        print("按 Ctrl+C 停止服务\n")
        print("=" * 60)
        
        # 使用subprocess启动服务
        subprocess.run([sys.executable, 'wsgi.py'], check=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 服务启动失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
        return False
    finally:
        os.chdir(original_dir)

def main():
    """主函数"""
    print("🔄 API服务器重启工具")
    print("=" * 40)
    print("更新内容:")
    print("✨ 增强了所有API接口的文档说明")
    print("✨ 添加了详细的参数说明和使用场景")
    print("✨ 完善了请求体参数的描述")
    print()
    
    # 停止现有服务器
    if stop_server():
        time.sleep(1)
        # 启动新服务器
        start_server()
    else:
        print("❌ 无法停止现有服务器，请手动停止后重新启动")

if __name__ == "__main__":
    main()