version: "3"
services:
  xingxi-algs-server:
    image: xingxi-algs-server:1.0.0
    container_name: xingxi-algs-server

    privileged: true
    restart: always

    environment:
      - PROCESSES=4
      - HTTP_TIMEOUT=60

    depends_on:
      - flask-api-demo-postgres
      - flask-api-demo-redis

    ports:
      - "2021:5000"

    volumes:
      - "/data/flask-api-demo/data:/data/data"
      - "/data/flask-api-demo/log:/data/log"
      - "./:/work"


networks:
  default:
    external: true
    name: casia