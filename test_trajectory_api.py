#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航迹行为识别API测试脚本
测试所有航迹分析相关的API接口
"""

import requests
import json
import time
from typing import List, Dict, Optional
import random
import math

class TrajectoryAPITester:
    """航迹API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000", api_token: str = None):
        """
        初始化API测试器
        Args:
            base_url: API服务器地址
            api_token: API认证令牌（如果需要）
        """
        self.base_url = base_url.rstrip('/')
        self.api_token = api_token
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # 如果有认证令牌，添加到headers
        if api_token:
            self.headers['Authorization'] = f'Bearer {api_token}'
    
    def _make_request(self, method: str, endpoint: str, data: dict = None, params: dict = None) -> dict:
        """
        发送HTTP请求的通用方法
        Args:
            method: HTTP方法 (GET, POST, etc.)
            endpoint: API端点
            data: 请求体数据
            params: URL参数
        Returns:
            响应数据字典
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            print(f"\n🔄 {method} {url}")
            if data:
                print(f"📤 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            if params:
                print(f"📋 查询参数: {params}")
            
            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            print(f"📥 响应状态码: {response.status_code}")
            
            # 解析响应
            try:
                result = response.json()
                print(f"📦 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return {
                    'success': response.status_code == 200,
                    'status_code': response.status_code,
                    'data': result
                }
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': 'Invalid JSON response',
                    'raw_response': response.text
                }
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败: 无法连接到 {url}")
            print("   请确保API服务器正在运行")
            return {
                'success': False,
                'error': 'Connection failed'
            }
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时")
            return {
                'success': False,
                'error': 'Request timeout'
            }
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        print(f"\n{'='*60}")
        print(f"🏥 测试1: 系统健康检查")
        print(f"{'='*60}")
        
        result = self._make_request('GET', '/api/v1/trajectory/health')
        
        if result['success']:
            data = result['data']
            print(f"✅ 健康检查通过")
            print(f"   系统状态: {data.get('data', {}).get('status', 'unknown')}")
            print(f"   版本: {data.get('data', {}).get('version', 'unknown')}")
            
            model_status = data.get('data', {}).get('model_status', {})
            print(f"   模型状态:")
            for model, status in model_status.items():
                status_icon = "✅" if status == "available" else "❌"
                print(f"     {status_icon} {model}: {status}")
            
            return True
        else:
            print(f"❌ 健康检查失败")
            return False
    
    def test_database_info(self) -> bool:
        """测试数据库信息查询接口"""
        print(f"\n{'='*60}")
        print(f"🗄️ 测试2: 数据库信息查询")
        print(f"{'='*60}")
        
        # 测试无参数查询
        result = self._make_request('GET', '/api/v1/trajectory/database/info')
        
        if result['success']:
            data = result['data']
            print(f"✅ 数据库信息查询成功")
            
            db_info = data.get('data', {}).get('database_info', {})
            print(f"   数据库类型: {db_info.get('database_type', 'unknown')}")
            print(f"   缓存数据库数: {db_info.get('cached_databases', 0)}")
            print(f"   支持的行为: {db_info.get('supported_actions', [])}")
            
            ships = data.get('data', {}).get('ships', [])
            if ships:
                print(f"   已缓存的舰船: {ships}")
            else:
                print(f"   已缓存的舰船: 无")
            
            return True
        else:
            print(f"❌ 数据库信息查询失败")
            return False
    
    def create_test_trajectory(self, trajectory_type: str = "linear") -> List[List[float]]:
        """
        创建测试航迹数据
        Args:
            trajectory_type: 航迹类型 ("linear", "circular", "zigzag", "stationary")
        Returns:
            坐标列表 [[纬度, 经度], ...]
        """
        if trajectory_type == "linear":
            # 线性航迹 (航渡模式)
            start_lat, start_lng = 35.1234, 139.5678
            coords = []
            for i in range(8):
                lat = start_lat + i * 0.001  # 向北移动
                lng = start_lng + i * 0.001  # 向东移动
                coords.append([lat, lng])
            return coords
            
        elif trajectory_type == "circular":
            # 圆形航迹 (巡航模式)
            center_lat, center_lng = 36.0, 140.0
            radius = 0.01  # 约1公里
            coords = []
            for i in range(8):
                angle = 2 * math.pi * i / 8
                lat = center_lat + radius * math.cos(angle)
                lng = center_lng + radius * math.sin(angle)
                coords.append([lat, lng])
            return coords
            
        elif trajectory_type == "stationary":
            # 静止航迹 (停靠模式)
            base_lat, base_lng = 36.8485, -76.2951  # 诺福克海军基地
            coords = []
            for i in range(6):
                # 添加微小的随机偏移模拟GPS误差
                lat = base_lat + random.uniform(-0.0001, 0.0001)
                lng = base_lng + random.uniform(-0.0001, 0.0001)
                coords.append([lat, lng])
            return coords
            
        elif trajectory_type == "zigzag":
            # 之字形航迹 (编队模式)
            start_lat, start_lng = 34.0, 135.0
            coords = []
            for i in range(8):
                lat = start_lat + i * 0.001
                lng = start_lng + (0.002 if i % 2 == 0 else -0.002)
                coords.append([lat, lng])
            return coords
            
        else:
            # 默认返回线性航迹
            return self.create_test_trajectory("linear")
    
    def test_trajectory_classification(self) -> bool:
        """测试航迹分类接口"""
        print(f"\n{'='*60}")
        print(f"🎯 测试3: 航迹行为分类")
        print(f"{'='*60}")
        
        test_cases = [
            {
                "name": "文森号航空母舰-线性航迹",
                "ship_name": "文森号航空母舰",
                "trajectory_type": "linear",
                "top_k": 5
            },
            {
                "name": "企业号航空母舰-圆形航迹", 
                "ship_name": "企业号航空母舰",
                "trajectory_type": "circular",
                "top_k": 3
            },
            {
                "name": "辽宁舰-停靠航迹",
                "ship_name": "辽宁舰", 
                "trajectory_type": "stationary",
                "top_k": 5
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            
            # 生成测试航迹
            trajectory_coords = self.create_test_trajectory(test_case['trajectory_type'])
            
            # 构建请求数据
            request_data = {
                "ship_name": test_case['ship_name'],
                "trajectory_coords": trajectory_coords,
                "top_k": test_case['top_k']
            }
            
            print(f"📍 航迹类型: {test_case['trajectory_type']}")
            print(f"🚢 舰船名称: {test_case['ship_name']}")
            print(f"📊 坐标点数: {len(trajectory_coords)}")
            print(f"🔍 Top-K: {test_case['top_k']}")
            
            # 发送分类请求
            result = self._make_request('POST', '/api/v1/trajectory/classify', data=request_data)
            
            if result['success']:
                data = result['data']
                classification_result = data.get('data', {})
                
                if classification_result.get('success', False):
                    print(f"✅ 分类成功")
                    print(f"   预测行为: {classification_result.get('predicted_action', 'unknown')}")
                    print(f"   置信度: {classification_result.get('confidence', 0):.2%}")
                    print(f"   处理时间: {classification_result.get('processing_time_ms', 0)}ms")
                    
                    # 显示相似样本
                    similar_samples = classification_result.get('similar_samples', [])
                    if similar_samples:
                        print(f"   相似样本:")
                        for j, sample in enumerate(similar_samples, 1):
                            print(f"     {j}. {sample.get('action', 'unknown')} (相似度: {sample.get('similarity', 0):.2%})")
                    
                    success_count += 1
                else:
                    print(f"❌ 分类失败: {classification_result.get('error', 'unknown error')}")
            else:
                print(f"❌ 请求失败")
            
            # 添加延迟，避免请求过快
            time.sleep(1)
        
        print(f"\n📊 分类测试总结: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
    
    def test_database_rebuild(self) -> bool:
        """测试数据库重建接口"""
        print(f"\n{'='*60}")
        print(f"🔧 测试4: 数据库重建")
        print(f"{'='*60}")
        
        print("⚠️ 注意: 数据库重建可能需要较长时间，请耐心等待...")
        
        # 测试重建指定舰船的数据库
        test_ship = "文森号航空母舰"
        request_data = {
            "ship_name": test_ship
        }
        
        print(f"🚢 重建舰船: {test_ship}")
        
        start_time = time.time()
        result = self._make_request('POST', '/api/v1/trajectory/database/rebuild', data=request_data)
        elapsed_time = int((time.time() - start_time) * 1000)
        
        if result['success']:
            data = result['data']
            rebuild_result = data.get('data', {})
            
            if rebuild_result.get('success', False):
                print(f"✅ 数据库重建成功")
                print(f"   舰船名称: {rebuild_result.get('ship_name', 'unknown')}")
                print(f"   构建时间: {rebuild_result.get('build_time_ms', 0)}ms")
                print(f"   总耗时: {elapsed_time}ms")
                
                # 显示数据库信息
                db_info = rebuild_result.get('database_info', {})
                if db_info:
                    print(f"   数据库信息:")
                    print(f"     总向量数: {db_info.get('total_vectors', 0)}")
                    print(f"     向量维度: {db_info.get('vector_dimension', 'unknown')}")
                    print(f"     行为类型数: {db_info.get('unique_actions', 0)}")
                
                return True
            else:
                print(f"❌ 数据库重建失败")
                return False
        else:
            print(f"❌ 重建请求失败")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print(f"\n🚀 开始航迹行为识别API完整测试")
        print(f"🌐 API服务器: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # 测试1: 健康检查
        results['health_check'] = self.test_health_check()
        
        # 测试2: 数据库信息
        results['database_info'] = self.test_database_info()
        
        # 测试3: 航迹分类
        results['trajectory_classification'] = self.test_trajectory_classification()
        
        # 测试4: 数据库重建 (可选，因为比较耗时)
        print(f"\n❓ 是否执行数据库重建测试？(这可能需要几分钟时间)")
        print("   输入 'y' 或 'yes' 执行，其他任意键跳过:")
        user_input = input().strip().lower()
        
        if user_input in ['y', 'yes']:
            results['database_rebuild'] = self.test_database_rebuild()
        else:
            print("⏭️ 跳过数据库重建测试")
            results['database_rebuild'] = None
        
        # 打印测试总结
        self.print_test_summary(results)
        
        return results
    
    def print_test_summary(self, results: Dict[str, bool]):
        """打印测试总结"""
        print(f"\n{'='*60}")
        print(f"📋 测试结果总结")
        print(f"{'='*60}")
        
        test_names = {
            'health_check': '系统健康检查',
            'database_info': '数据库信息查询', 
            'trajectory_classification': '航迹行为分类',
            'database_rebuild': '数据库重建'
        }
        
        passed = 0
        total = 0
        
        for test_key, test_name in test_names.items():
            result = results.get(test_key)
            if result is None:
                status = "⏭️ 跳过"
            elif result:
                status = "✅ 通过"
                passed += 1
                total += 1
            else:
                status = "❌ 失败"
                total += 1
            
            print(f"  {status} {test_name}")
        
        if total > 0:
            success_rate = (passed / total) * 100
            print(f"\n🎯 测试通过率: {passed}/{total} ({success_rate:.1f}%)")
            
            if success_rate == 100:
                print("🎉 所有测试都通过了！API接口工作正常。")
            elif success_rate >= 75:
                print("✨ 大部分测试通过，API基本功能正常。")
            else:
                print("⚠️ 多个测试失败，请检查API服务和相关依赖。")
        else:
            print("ℹ️ 没有执行任何测试")

def main():
    """主函数"""
    print("🧪 航迹行为识别API测试工具")
    print("=" * 60)
    
    # 配置API服务器地址
    print("请配置API服务器信息:")
    
    # 获取服务器地址
    default_url = "http://localhost:5000"
    server_url = input(f"API服务器地址 (默认: {default_url}): ").strip()
    if not server_url:
        server_url = default_url
    
    # 获取认证令牌（如果需要）
    api_token = input("API认证令牌 (可选，直接回车跳过): ").strip()
    if not api_token:
        api_token = None
    
    # 创建测试器并运行测试
    tester = TrajectoryAPITester(base_url=server_url, api_token=api_token)
    results = tester.run_all_tests()
    
    print(f"\n🏁 测试完成！")

if __name__ == "__main__":
    main()