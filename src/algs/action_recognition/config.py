# -*- coding: utf-8 -*-
"""
舰船行为识别算法配置文件
包含所有可配置的参数和常量
"""

# ==================== API 配置 ====================
# 外部数据源API配置
API_CONFIG = {
    "BASE_URL": "http://**************:23071",
    "ENDPOINT": "/api/v1/ship/active/trend",
    "TIMEOUT": 30,  # 请求超时时间（秒）
    "PROXIES": {
        'http': None,
        'https': None
    },
    # 默认查询时间范围
    "DEFAULT_START_TIME": "1990-01-01",
    "DEFAULT_END_TIME": "2025-08-03"
}

# ==================== 行为识别配置 ====================
# 支持的舰船行为类型
BEHAVIOR_CONFIG = {
    "CANDIDATE_ACTIONS": ["编队", "航渡", "停靠", "巡航"],
    "DEFAULT_SHIP_NAME": "文森号航空母舰"
}

# ==================== 时间窗口配置 ====================
# 时间窗口切片参数
WINDOW_CONFIG = {
    "WINDOW_HOURS": 12,      # 时间窗口大小（小时）
    "STRIDE_HOURS": 12,      # 滑动步长（小时）
    "MIN_BLOCK_SIZE": 1      # 最小区块大小
}

# ==================== 地理哈希配置 ====================
# 坐标文本化参数
GEOHASH_CONFIG = {
    "PRECISION": 7,          # Geohash精度（6-8位，默认7位）
    "SEPARATOR": " "         # Geohash序列分隔符
}

# ==================== 向量化模型配置 ====================
# 文本向量化相关配置
EMBEDDING_CONFIG = {
    # 模型类型优先级：qwen > sentence_transformer > hash
    "MODEL_TYPE": "qwen",
    
    # Qwen3-Embedding 模型配置
    "QWEN_MODEL": {
        "GGUF_MODEL_PATH": "models/qwen3-embedding-0.6b.gguf",
        "HUGGINGFACE_MODEL": "Qwen/Qwen3-Embedding-0.6B",
        "MAX_LENGTH": 512
    },
    
    # Sentence Transformers 模型配置
    "SENTENCE_TRANSFORMER_MODEL": "sentence-transformers/all-MiniLM-L6-v2",
    
    # 哈希向量化配置（备用方案）
    "HASH_VECTOR": {
        "DIMENSION": 384,
        "HASH_FUNCTIONS": ["md5", "sha1", "sha256"],
        "NORMALIZATION_RANGE": 1000  # 归一化范围
    },
    
    # 向量化进度显示配置
    "PROGRESS_INTERVAL": 10  # 每处理多少个向量显示一次进度
}

# ==================== 向量数据库配置 ====================
# 基础向量数据库配置
VECTOR_DB_CONFIG = {
    "DEFAULT_DB_PATH": "vector_database",
    "DATABASE_DIR": "databases",
    
    # 文件扩展名
    "DB_EXTENSION": ".db",
    "VECTORS_EXTENSION": "_vectors.pkl",
    "METADATA_EXTENSION": "_metadata.json",
    
    # 相似度搜索配置
    "DEFAULT_TOP_K": 5,
    "DEFAULT_SIMILARITY_THRESHOLD": 0.0,
    "SIMILARITY_METHODS": ["cosine"]
}

# ==================== FAISS 向量数据库配置 ====================
# FAISS 高性能向量数据库配置
FAISS_CONFIG = {
    "DEFAULT_DB_PATH": "faiss_vector_database",
    "INDEX_EXTENSION": "_faiss.index",
    
    # FAISS 索引类型配置
    "INDEX_TYPES": {
        "FLAT": {
            "name": "Flat",
            "description": "精确搜索，适合小数据集",
            "max_vectors": 10000
        },
        "IVF": {
            "name": "IVF", 
            "description": "倒排文件索引，适合中等数据集",
            "nlist_ratio": 10,  # 聚类中心数 = 向量数 / nlist_ratio
            "max_nlist": 100,
            "nprobe": 10
        },
        "HNSW": {
            "name": "HNSW",
            "description": "分层导航小世界图，适合大数据集", 
            "m": 32
        }
    },
    
    "DEFAULT_INDEX_TYPE": "IVF",
    
    # 相似度转换配置
    "SIMILARITY_CONVERSION": {
        "DISTANCE_THRESHOLD": 5.0,  # 小距离阈值
        "EXP_DECAY_FACTOR": 10.0,   # 指数衰减因子
    }
}

# ==================== 在线推理配置 ====================
# 在线推理引擎配置
ONLINE_INFERENCE_CONFIG = {
    # 数据库文件名模式
    "DB_NAME_PATTERNS": [
        "databases/*{ship_name}*",
        "databases/online_faiss_db_*",
        "databases/faiss_vector_db_*"
    ],
    
    # 分类决策配置
    "CLASSIFICATION": {
        "VOTE_WEIGHT": 0.4,         # 投票权重
        "SIMILARITY_WEIGHT": 0.6,   # 相似度权重
        "MIN_SIMILARITY": 0.3       # 最小相似度阈值
    },
    
    # 进度显示配置
    "PROGRESS_DISPLAY": {
        "BLOCK_INTERVAL": 5,        # 每处理多少个区块显示一次进度
        "MAX_SAMPLE_DISPLAY": 3     # 最多显示多少个相似样本
    }
}

# ==================== 数据库表结构配置 ====================
# SQLite 数据库表结构
DATABASE_SCHEMA = {
    "VECTOR_METADATA_TABLE": """
        CREATE TABLE IF NOT EXISTS vector_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ship_name TEXT NOT NULL,
            action TEXT NOT NULL,
            identity_text TEXT,
            geohash_sentence TEXT,
            coordinate_count INTEGER,
            vector_dim INTEGER,
            vector_norm REAL,
            time_range TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            vector_id INTEGER UNIQUE
        )
    """,
    
    "FAISS_METADATA_TABLE": """
        CREATE TABLE IF NOT EXISTS vector_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            faiss_id INTEGER UNIQUE,
            ship_name TEXT NOT NULL,
            action TEXT NOT NULL,
            identity_text TEXT,
            geohash_sentence TEXT,
            coordinate_count INTEGER,
            vector_dim INTEGER,
            vector_norm REAL,
            time_range TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """,
    
    "BEHAVIOR_STATS_TABLE": """
        CREATE TABLE IF NOT EXISTS behavior_stats (
            action TEXT PRIMARY KEY,
            total_samples INTEGER,
            avg_vector_dim REAL,
            avg_coordinate_count REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """,
    
    # 索引配置
    "INDEXES": [
        "CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)",
        "CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)",
        "CREATE INDEX IF NOT EXISTS idx_vector_dim ON vector_metadata(vector_dim)",
        "CREATE INDEX IF NOT EXISTS idx_faiss_id ON vector_metadata(faiss_id)"
    ]
}

# ==================== 演示配置 ====================
# 演示和测试用配置
DEMO_CONFIG = {
    # 示例航迹坐标
    "SAMPLE_TRAJECTORIES": {
        "LINEAR_MOVEMENT": [
            (35.1234, 139.5678),
            (35.1244, 139.5688),
            (35.1254, 139.5698),
            (35.1264, 139.5708),
            (35.1274, 139.5718),
            (35.1284, 139.5728),
            (35.1294, 139.5738),
            (35.1304, 139.5748)
        ],
        "DOCKING_PATTERN": [
            (36.8485, -76.2951),  # 诺福克海军基地附近
            (36.8485, -76.2951),
            (36.8485, -76.2951),
            (36.8485, -76.2951),
            (36.8485, -76.2951),
            (36.8485, -76.2951)
        ]
    },
    
    # 演示用舰船名称
    "SAMPLE_SHIPS": ["文森号航空母舰", "企业号航空母舰"],
    
    # 演示参数
    "DEMO_TOP_K": 5,
    "MAX_DEMO_SAMPLES": 3
}

# ==================== 日志和调试配置 ====================
# 日志配置
LOGGING_CONFIG = {
    "ENABLE_PROGRESS_LOG": True,
    "ENABLE_DEBUG_LOG": False,
    "LOG_VECTOR_GENERATION": True,
    "LOG_DATABASE_OPERATIONS": True,
    "LOG_SIMILARITY_SEARCH": True
}

# ==================== 性能优化配置 ====================
# 性能相关配置
PERFORMANCE_CONFIG = {
    # 向量维度处理
    "VECTOR_PADDING": True,      # 是否进行向量填充
    "VECTOR_TRUNCATION": True,   # 是否进行向量截断
    
    # 批处理配置
    "BATCH_SIZE": 100,          # 批处理大小
    "MAX_MEMORY_USAGE": "1GB",   # 最大内存使用量
    
    # 缓存配置
    "ENABLE_MODEL_CACHE": True,  # 是否启用模型缓存
    "ENABLE_DB_CACHE": True,     # 是否启用数据库缓存
}

# ==================== 版本信息 ====================
VERSION_INFO = {
    "VERSION": "1.0.0",
    "BUILD_DATE": "2025-01-07",
    "AUTHOR": "AI Assistant",
    "DESCRIPTION": "舰船行为识别算法配置文件"
}

# ==================== 配置验证函数 ====================
def validate_config():
    """验证配置文件的有效性"""
    errors = []
    
    # 验证API配置
    if not API_CONFIG["BASE_URL"]:
        errors.append("API_CONFIG.BASE_URL 不能为空")
    
    # 验证行为类型配置
    if not BEHAVIOR_CONFIG["CANDIDATE_ACTIONS"]:
        errors.append("BEHAVIOR_CONFIG.CANDIDATE_ACTIONS 不能为空")
    
    # 验证时间窗口配置
    if WINDOW_CONFIG["WINDOW_HOURS"] <= 0:
        errors.append("WINDOW_CONFIG.WINDOW_HOURS 必须大于0")
    
    # 验证地理哈希配置
    if not (6 <= GEOHASH_CONFIG["PRECISION"] <= 8):
        errors.append("GEOHASH_CONFIG.PRECISION 必须在6-8之间")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
    
    return True

# ==================== 配置获取函数 ====================
def get_config(section=None):
    """
    获取配置信息
    Args:
        section: 配置节名称，如果为None则返回所有配置
    Returns:
        配置字典
    """
    all_configs = {
        "API": API_CONFIG,
        "BEHAVIOR": BEHAVIOR_CONFIG,
        "WINDOW": WINDOW_CONFIG,
        "GEOHASH": GEOHASH_CONFIG,
        "EMBEDDING": EMBEDDING_CONFIG,
        "VECTOR_DB": VECTOR_DB_CONFIG,
        "FAISS": FAISS_CONFIG,
        "ONLINE_INFERENCE": ONLINE_INFERENCE_CONFIG,
        "DATABASE_SCHEMA": DATABASE_SCHEMA,
        "DEMO": DEMO_CONFIG,
        "LOGGING": LOGGING_CONFIG,
        "PERFORMANCE": PERFORMANCE_CONFIG,
        "VERSION": VERSION_INFO
    }
    
    if section is None:
        return all_configs
    
    return all_configs.get(section.upper(), {})

# 启动时验证配置
if __name__ == "__main__":
    try:
        validate_config()
        print("✅ 配置文件验证通过")
        print(f"📋 配置版本: {VERSION_INFO['VERSION']}")
        print(f"📅 构建日期: {VERSION_INFO['BUILD_DATE']}")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
