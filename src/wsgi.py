# -*- coding: utf-8 -*-
# <AUTHOR> llc
# @Time    : 2020/5/4 15:53
from flask import redirect, url_for
from flask.cli import click, with_appcontext
from flask_migrate import Migrate
from geoalchemy2.alembic_helpers import include_object, render_item, writer
from sqlalchemy import select

from app import create_app
from app.model import db

app = create_app()

# compare_server_default=True,include_object=include_object,render_item=render_item,process_revision_directives=writer
migrate = Migrate(app, db, render_as_batch=False,
                  # configure for geoalchemy2
                  include_object=include_object,
                  render_item=render_item,
                  process_revision_directives=writer)


@app.route("/")
def index():
    """根目录重定向到openapi"""
    return redirect(url_for("openapi.openapi"))


@app.cli.command("test")
@click.argument("a")
@click.option("--b", default="b", help="option help")
def test(a, b):
    """test flask cli command"""
    print(a)
    print(b)


@app.cli.command("init_db")
@with_appcontext
def init_db():
    """初始化数据库"""
    from app.model.user import User, Role
    user = db.session.execute(select(User).where(User.username == "super")).scalar()
    if user:
        print("超级管理员已存在.")
    else:
        user = User()
        user.username = "super"
        user.password = "123456"
        user.is_super = True
        user.is_active = True
        db.session.add(user)
        db.session.commit()
        print("添加超级管理员成功.")

    role = db.session.execute(select(Role).where(Role.name == "普通用户")).scalar()
    if role:
        print("普通用户角色已存在.")
    else:
        role = Role()
        role.name = "普通用户"
        role.describe = "默认权限组"
        db.session.add(role)
        db.session.commit()
        print("添加普通用户角色成功.")


@app.cli.command("register_permission")
@with_appcontext
def register_permission():
    """注册权限"""
    from app.utils.jwt_tools import permissions
    from app.model import db
    from app.model.user import Permission

    for name, module, uuid in permissions:
        permission = db.session.execute(select(Permission).where(Permission.name == name)).scalar()
        if permission:
            print(f"{permission} is exists.")
            continue
        permission = Permission()
        permission.name = name
        permission.module = module
        permission.uuid = uuid
        db.session.add(permission)
        db.session.commit()
        print(f"{name} register success.")


if __name__ == "__main__":
    import sys
    import os
    
    # 自动检测并切换到正确的工作目录
    current_dir = os.getcwd()
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 如果当前目录不是src目录，自动切换
    if not current_dir.endswith('src') and os.path.basename(script_dir) == 'src':
        print(f"🔄 自动切换工作目录: {current_dir} -> {script_dir}")
        os.chdir(script_dir)
        # 确保项目根目录在Python路径中
        project_root = os.path.dirname(script_dir)
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
    
    print("🌟 航迹行为识别API服务")
    print("=" * 50)
    print("🚀 正在启动Flask服务...")
    print("📡 服务地址: http://localhost:5000")
    print("📚 API文档: http://localhost:5000/openapi/swagger")
    print("🎯 主要API端点:")
    print("  - GET  /api/v1/trajectory/health (健康检查)")
    print("  - POST /api/v1/trajectory/classify (航迹分类)")
    print("  - GET  /api/v1/trajectory/database/info (数据库信息)")
    print("  - POST /api/v1/trajectory/database/rebuild (重建数据库)")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # app.config["SQLALCHEMY_ECHO"] = True
        # 禁用调试模式的自动重启功能，避免路径问题
        app.run("0.0.0.0", 5000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
