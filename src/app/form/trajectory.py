
from typing import List, Tuple, Optional
from pydantic import BaseModel, Field, validator

from app.form import JsonResponse


class CoordinatePoint(BaseModel):
    """坐标点模型"""
    lat: float = Field(..., ge=-90, le=90, description="纬度 (-90 到 90)")
    lng: float = Field(..., ge=-180, le=180, description="经度 (-180 到 180)")


class TrajectoryClassificationBody(BaseModel):
    """航迹分类请求体"""
    ship_name: str = Field(..., min_length=1, max_length=100, description="舰船名称")
    trajectory_coords: List[List[float]] = Field(
        ..., 
        min_items=1, 
        max_items=100,
        description="航迹坐标列表，格式为 [[纬度, 经度], [纬度, 经度], ...]"
    )
    top_k: Optional[int] = Field(5, ge=1, le=20, description="返回最相似的前k个结果 (1-20)")
    
    @validator('trajectory_coords')
    def validate_coordinates(cls, v):
        """验证坐标格式"""
        for i, coord in enumerate(v):
            if not isinstance(coord, list) or len(coord) != 2:
                raise ValueError(f"坐标点 {i} 格式错误，应为 [纬度, 经度]")
            
            lat, lng = coord
            if not isinstance(lat, (int, float)) or not isinstance(lng, (int, float)):
                raise ValueError(f"坐标点 {i} 包含非数字值")
            
            if not (-90 <= lat <= 90):
                raise ValueError(f"坐标点 {i} 纬度超出范围 (-90 到 90): {lat}")
            
            if not (-180 <= lng <= 180):
                raise ValueError(f"坐标点 {i} 经度超出范围 (-180 到 180): {lng}")
                
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "ship_name": "文森号航空母舰",
                "trajectory_coords": [
                    [35.1234, 139.5678],
                    [35.1244, 139.5688],
                    [35.1254, 139.5698],
                    [35.1264, 139.5708],
                    [35.1274, 139.5718]
                ],
                "top_k": 5
            }
        }


class TrajectoryClassificationResponse(JsonResponse):
    """航迹分类响应体"""
    data: Optional[dict] = Field(None, description="分类结果数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": 0,
                "message": "ok",
                "data": {
                    "success": True,
                    "classification": {
                        "predicted_action": "巡航",
                        "confidence": 0.85,
                        "confidence_level": "高"
                    },
                    "processing": {
                        "ship_name": "文森号航空母舰",
                        "coordinate_count": 8,
                        "processing_time_ms": 1250,
                        "vector_dimension": 1024
                    },
                    "analysis": {
                        "action_scores": {
                            "巡航": {
                                "vote_count": 3,
                                "vote_ratio": 0.6,
                                "avg_similarity": 0.85,
                                "max_similarity": 0.92,
                                "combined_score": 0.75
                            },
                            "航渡": {
                                "vote_count": 2,
                                "vote_ratio": 0.4,
                                "avg_similarity": 0.72,
                                "max_similarity": 0.78,
                                "combined_score": 0.59
                            }
                        },
                        "similar_samples": [
                            {
                                "sample_id": 1,
                                "action": "巡航",
                                "similarity": 0.92,
                                "metadata": {
                                    "vector_dim": 1024,
                                    "coordinate_count": 6
                                }
                            },
                            {
                                "sample_id": 2,
                                "action": "巡航",
                                "similarity": 0.85,
                                "metadata": {
                                    "vector_dim": 1024,
                                    "coordinate_count": 8
                                }
                            }
                        ]
                    },
                    "summary": "识别为巡航行为，置信度85.0%(高)，基于60%的历史样本匹配"
                }
            }
        }


class TrajectoryHealthResponse(JsonResponse):
    """系统健康检查响应"""
    data: Optional[dict] = Field(None, description="系统状态信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": 0,
                "message": "ok",
                "data": {
                    "status": "healthy",
                    "version": "1.0.0",
                    "model_status": {
                        "qwen_embedding": "available",
                        "faiss_database": "available"
                    },
                    "supported_actions": ["编队", "航渡", "停靠", "巡航"],
                    "max_coordinates": 100,
                    "default_precision": 7
                }
            }
        }


class ShipDatabaseQuery(BaseModel):
    """舰船数据库查询参数"""
    ship_name: Optional[str] = Field(None, max_length=100, description="舰船名称 (可选)")
    action_filter: Optional[str] = Field(None, description="行为类型过滤 (编队/航渡/停靠/巡航)")
    limit: Optional[int] = Field(10, ge=1, le=100, description="返回记录数限制")


class ShipDatabaseResponse(JsonResponse):
    """舰船数据库查询响应"""
    data: Optional[dict] = Field(None, description="数据库信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": 0,
                "message": "ok",
                "data": {
                    "database_info": {
                        "total_vectors": 1250,
                        "unique_ships": 5,
                        "unique_actions": 4,
                        "vector_dimension": 384,
                        "database_type": "FAISS"
                    },
                    "behavior_stats": {
                        "巡航": {
                            "total_samples": 450,
                            "avg_vector_dim": 384.0,
                            "avg_coordinate_count": 8.5
                        },
                        "停靠": {
                            "total_samples": 280,
                            "avg_vector_dim": 384.0,
                            "avg_coordinate_count": 12.3
                        }
                    },
                    "ships": [
                        "文森号航空母舰",
                        "企业号航空母舰"
                    ]
                }
            }
        }


class DatabaseRebuildBody(BaseModel):
    """数据库重建请求体"""
    ship_name: str = Field(..., min_length=1, max_length=100, description="要重建数据库的舰船名称")
    
    class Config:
        json_schema_extra = {
            "example": {
                "ship_name": "文森号航空母舰"
            }
        }


class DatabaseRebuildResponse(JsonResponse):
    """数据库重建响应体"""
    data: Optional[dict] = Field(None, description="重建结果数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": 0,
                "message": "ok", 
                "data": {
                    "success": True,
                    "ship_name": "文森号航空母舰",
                    "build_time_ms": 120000,
                    "database_info": {
                        "total_vectors": 1500,
                        "vector_dimension": 384,
                        "unique_actions": 4,
                        "unique_ships": 1
                    }
                }
            }
        }