from typing import Optional
from pydantic import BaseModel,Field
from app.form import JsonResponse

class HelloQuery(BaseModel):
    """GET 查询参数：?name=张三"""
    name:Optional[str] = Field("世界",min_length=1,max_length=50,description="名字")

class HelloBody(BaseModel):
    """POST 请求体 """
    message:str = Field(...,min_length=1,max_length=200,description="要回显的消息")

class HelloResponse(JsonResponse):
    """统一响应体复用全局JsonResponse"""
    data:Optional[dict]=None