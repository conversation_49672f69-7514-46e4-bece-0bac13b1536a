# 航迹行为识别 API 接口文档

## 概述

本API提供舰船航迹行为分类服务，能够根据输入的12小时航迹坐标数据，自动识别舰船的行为模式，包括：**编队**、**航渡**、**停靠**、**巡航** 四种行为类型。

### 基础信息

- **API版本**: v1
- **基础路径**: `/api/v1/trajectory`
- **认证方式**: JWT <PERSON>er <PERSON> (如果启用)
- **数据格式**: JSON
- **字符编码**: UTF-8

## API 端点列表

### 1. 航迹行为分类

**POST** `/api/v1/trajectory/classify`

对输入的舰船航迹数据进行实时行为分类。

#### 请求参数

```json
{
  "ship_name": "文森号航空母舰",
  "trajectory_coords": [
    [35.1234, 139.5678],
    [35.1244, 139.5688],
    [35.1254, 139.5698],
    [35.1264, 139.5708],
    [35.1274, 139.5718]
  ],
  "top_k": 5
}
```

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `ship_name` | string | 是 | 舰船名称，1-100字符 |
| `trajectory_coords` | array | 是 | 坐标数组，格式为 `[[纬度, 经度], ...]`，1-100个坐标点 |
| `top_k` | integer | 否 | 返回最相似的前k个历史样本，默认5，范围1-20 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "success": true,
    "ship_name": "文森号航空母舰",
    "predicted_action": "巡航",
    "confidence": 0.8547,
    "coordinate_count": 5,
    "processing_time_ms": 1250,
    "explanation": "🎯 **预测行为**: 巡航\n🔥 **置信度**: 85.5%\n\n📊 **判断依据**:\n  • 巡航:\n    - 投票数: 3/5 (60.0%)\n    - 平均相似度: 85.5%\n    - 最高相似度: 92.0%\n    - 综合得分: 75.3%",
    "action_scores": {
      "巡航": {
        "vote_count": 3,
        "vote_ratio": 0.6,
        "avg_similarity": 0.855,
        "max_similarity": 0.92,
        "combined_score": 0.753
      },
      "航渡": {
        "vote_count": 2,
        "vote_ratio": 0.4,
        "avg_similarity": 0.723,
        "max_similarity": 0.781,
        "combined_score": 0.594
      }
    },
    "similar_samples": [
      {
        "id": 1,
        "similarity": 0.92,
        "action": "巡航"
      },
      {
        "id": 2,
        "similarity": 0.855,
        "action": "巡航"
      }
    ]
  }
}
```

**失败响应 (400)**:
```json
{
  "code": 400,
  "message": "输入数据验证失败: 坐标点 0 纬度超出范围 (-90 到 90): 200.0",
  "data": {
    "success": false,
    "error": "validation_error",
    "processing_time_ms": 45
  }
}
```

### 2. 系统健康检查

**GET** `/api/v1/trajectory/health`

检查航迹行为识别系统的运行状态和各个组件的可用性。

#### 响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "model_status": {
      "behavior_recognition": "available",
      "faiss_database": "available",
      "transformers": "available",
      "sentence_transformers": "unavailable",
      "llama_cpp": "available"
    },
    "supported_actions": ["编队", "航渡", "停靠", "巡航"],
    "max_coordinates": 100,
    "default_precision": 7,
    "coordinate_format": "[[纬度, 经度], ...]",
    "cached_databases": 2
  }
}
```

### 3. 获取数据库信息

**GET** `/api/v1/trajectory/database/info`

获取向量数据库的统计信息，包括已缓存的舰船数据和行为统计。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `ship_name` | string | 否 | 舰船名称过滤 |
| `action_filter` | string | 否 | 行为类型过滤 (编队/航渡/停靠/巡航) |
| `limit` | integer | 否 | 返回记录数限制，默认10 |

#### 响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "database_info": {
      "cached_databases": 2,
      "database_type": "FAISS",
      "supported_actions": ["编队", "航渡", "停靠", "巡航"],
      "total_vectors": 1250,
      "unique_actions": 4,
      "unique_ships": 2,
      "vector_dimension": 384
    },
    "behavior_stats": {
      "巡航": {
        "total_samples": 450,
        "avg_vector_dim": 384.0,
        "avg_coordinate_count": 8.5
      },
      "停靠": {
        "total_samples": 280,
        "avg_vector_dim": 384.0,
        "avg_coordinate_count": 12.3
      }
    },
    "ships": [
      "文森号航空母舰",
      "里根号航空母舰"
    ]
  }
}
```

### 4. 重建舰船数据库

**POST** `/api/v1/trajectory/database/rebuild`

强制重新获取指定舰船的历史数据并重建向量数据库（管理员功能）。

#### 请求参数

```json
{
  "ship_name": "文森号航空母舰"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "success": true,
    "ship_name": "文森号航空母舰",
    "build_time_ms": 15420,
    "database_info": {
      "total_vectors": 856,
      "unique_actions": 4,
      "vector_dimension": 384,
      "database_type": "FAISS"
    }
  }
}
```

## 使用示例

### Python 示例

```python
import requests
import json

# API基础配置
BASE_URL = "http://localhost:5000"
API_PREFIX = "/api/v1/trajectory"
HEADERS = {'Content-Type': 'application/json'}

# 1. 健康检查
health_url = f"{BASE_URL}{API_PREFIX}/health"
response = requests.get(health_url, headers=HEADERS)
print("系统状态:", response.json())

# 2. 航迹分类
classify_url = f"{BASE_URL}{API_PREFIX}/classify"

# 巡航航迹示例
payload = {
    "ship_name": "文森号航空母舰",
    "trajectory_coords": [
        [35.1234, 139.5678],  # 日本海域
        [35.1244, 139.5688],
        [35.1254, 139.5698],
        [35.1264, 139.5708],
        [35.1274, 139.5718],
        [35.1284, 139.5728],
        [35.1294, 139.5738],
        [35.1304, 139.5748]
    ],
    "top_k": 5
}

response = requests.post(classify_url, json=payload, headers=HEADERS)
result = response.json()

if result["code"] == 0 and result["data"]["success"]:
    data = result["data"]
    print(f"预测行为: {data['predicted_action']}")
    print(f"置信度: {data['confidence']:.2%}")
    print(f"处理时间: {data['processing_time_ms']}ms")
else:
    print(f"分类失败: {result['message']}")
```

### cURL 示例

```bash
# 健康检查
curl -X GET "http://localhost:5000/api/v1/trajectory/health" \
     -H "Content-Type: application/json"

# 航迹分类
curl -X POST "http://localhost:5000/api/v1/trajectory/classify" \
     -H "Content-Type: application/json" \
     -d '{
       "ship_name": "文森号航空母舰",
       "trajectory_coords": [
         [35.1234, 139.5678],
         [35.1244, 139.5688],
         [35.1254, 139.5698]
       ],
       "top_k": 3
     }'
```

## 错误码说明

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数验证失败或数据格式错误 |
| 500 | 服务器错误 | 内部处理错误或服务不可用 |
| 503 | 服务不可用 | 航迹识别服务未启动或依赖组件缺失 |

### 常见错误示例

#### 坐标格式错误
```json
{
  "code": 400,
  "message": "输入数据验证失败: 坐标点 0 格式错误，应为 [纬度, 经度]",
  "data": {
    "success": false,
    "error": "validation_error"
  }
}
```

#### 坐标超出范围
```json
{
  "code": 400,
  "message": "输入数据验证失败: 坐标点 1 纬度超出范围 (-90 到 90): 95.0",
  "data": {
    "success": false,
    "error": "validation_error"
  }
}
```

#### 服务不可用
```json
{
  "code": 503,
  "message": "航迹行为识别服务不可用，请检查相关依赖",
  "data": {
    "success": false,
    "error": "behavior_recognition_unavailable"
  }
}
```

## 技术说明

### 支持的行为类型

| 行为类型 | 描述 | 典型特征 |
|----------|------|----------|
| **编队** | 多舰协同行动 | 多个舰船保持相对位置，同步移动 |
| **航渡** | 长距离航行 | 直线或规划路线移动，速度相对稳定 |
| **停靠** | 在港口或基地停泊 | 位置相对固定，只有微小位移 |
| **巡航** | 定期巡逻航行 | 规律性移动模式，可能包含巡逻路线 |

### 数据要求

- **时间跨度**: 建议提供12小时的航迹数据
- **坐标点数**: 1-100个坐标点，推荐5-50个
- **坐标精度**: 支持小数点后6位精度
- **坐标格式**: WGS84地理坐标系，格式为 `[纬度, 经度]`

### 性能指标

- **响应时间**: 通常在1-3秒内完成分类
- **准确率**: 基于历史数据训练，准确率取决于训练样本质量
- **并发处理**: 支持多用户同时请求
- **缓存机制**: 舰船历史数据会被缓存以提高响应速度

### 技术架构

- **向量化模型**: 支持 Qwen3-Embedding、Sentence-Transformers 等
- **向量数据库**: 优先使用 FAISS 高性能索引，回退到基础向量数据库
- **相似度算法**: 余弦相似度计算
- **分类决策**: 基于投票机制和相似度加权的综合评分

## 注意事项

1. **首次使用**: 系统首次处理某个舰船时，需要从在线接口获取历史数据并构建向量数据库，耗时较长（10-30秒）
2. **数据质量**: 分类准确度依赖于历史训练数据的质量和数量
3. **网络依赖**: 首次构建数据库时需要网络连接获取历史数据
4. **存储空间**: 向量数据库文件会占用一定磁盘空间
5. **计算资源**: 向量化和相似度计算需要一定的CPU和内存资源

## 更新历史

- **v1.0.0**: 初始版本，支持四种基本行为分类
- 后续版本将支持更多行为类型和优化分类算法