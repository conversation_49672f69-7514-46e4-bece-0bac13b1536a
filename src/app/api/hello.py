from flask_openapi3 import APIBlueprint,Tag
from app.config import API_PREFIX
from app.utils.response import response
from app.form.hello import HelloQuery,HelloBody,HelloResponse

#基本信息
__version__="/v1"
__bp__="/hello"
url_prefix = API_PREFIX+__version__+__bp__
tag =Tag(name="hello示例",description="hello示例入口")


#注意：不加abp_security,默认不需要JWT
api =APIBlueprint(__bp__,__name__,url_prefix=url_prefix,abp_tags=[tag])

@api.get("/ping",responses={"200":HelloResponse})
def ping():
    """健康探测:返回 pong"""
    return response(data={"message":"pong"})

@api.get("/greet",responses={"200":HelloResponse})
def greet(query:HelloQuery):
    """问候：?name=张三 """
    name = query.name or "世界"
    return response(data={"greeting":f"你好,{name}!"})

@api.post("/echo",responses={"200":HelloResponse})
def echo(body:HelloBody):
    """回显： POST JSON {message:"***"}"""
    return response(data={"message":body.message})







