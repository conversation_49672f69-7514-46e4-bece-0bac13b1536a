
import time
import sys
import os
from flask_openapi3 import APIBlueprint, Tag

# 添加项目根目录到路径，以便导入behavior recognition模块
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

from app.config import API_PREFIX, JWT
from app.form.trajectory import (
    TrajectoryClassificationBody, 
    TrajectoryClassificationResponse,
    TrajectoryHealthResponse,
    ShipDatabaseQuery,
    ShipDatabaseResponse,
    DatabaseRebuildBody,
    DatabaseRebuildResponse
)
from app.utils.response import response
from app.utils.exceptions import ParameterException

# 导入航迹行为识别核心功能
try:
    # 由于文件名包含空格，需要使用 importlib 导入
    import importlib.util
    import sys
    
    # 构建完整的文件路径
    behavior_file_path = os.path.join(project_root, "src", "behavior recognition.py")
    
    # 使用 importlib 动态导入
    spec = importlib.util.spec_from_file_location("behavior_recognition", behavior_file_path)
    behavior_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(behavior_module)
    
    # 从模块中获取需要的函数和变量
    online_trajectory_classification = behavior_module.online_trajectory_classification
    get_online_inference_engine = behavior_module.get_online_inference_engine
    OnlineInferenceEngine = behavior_module.OnlineInferenceEngine
    FAISS_AVAILABLE = behavior_module.FAISS_AVAILABLE
    TRANSFORMERS_AVAILABLE = behavior_module.TRANSFORMERS_AVAILABLE
    SENTENCE_TRANSFORMERS_AVAILABLE = behavior_module.SENTENCE_TRANSFORMERS_AVAILABLE
    LLAMA_CPP_AVAILABLE = behavior_module.LLAMA_CPP_AVAILABLE
    
    BEHAVIOR_RECOGNITION_AVAILABLE = True
    print("✅ 航迹行为识别模块加载成功")
except Exception as e:
    print(f"⚠️ 航迹行为识别模块导入失败: {e}")
    BEHAVIOR_RECOGNITION_AVAILABLE = False
    # 设置默认值
    FAISS_AVAILABLE = False
    TRANSFORMERS_AVAILABLE = False
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    LLAMA_CPP_AVAILABLE = False

__version__ = "/v1"
__bp__ = "/trajectory"
url_prefix = API_PREFIX + __version__ + __bp__
tag = Tag(name="航迹行为识别", description="舰船航迹行为分类与分析")
api = APIBlueprint(__bp__, __name__, url_prefix=url_prefix, abp_tags=[tag], abp_security=JWT)


def _get_confidence_level(confidence: float) -> str:
    """根据置信度返回等级描述"""
    if confidence >= 0.8:
        return "高"
    elif confidence >= 0.6:
        return "中"
    elif confidence >= 0.4:
        return "低"
    else:
        return "很低"


def _generate_summary(classification: dict, analysis: dict) -> str:
    """生成简洁的分类总结"""
    action = classification.get('predicted_action', '未知')
    confidence = classification.get('confidence', 0)
    confidence_level = classification.get('confidence_level', '未知')
    
    summary = f"识别为{action}行为，置信度{confidence:.1%}({confidence_level})"
    
    # 添加主要依据
    if 'action_scores' in analysis:
        action_scores = analysis['action_scores']
        if action in action_scores:
            vote_ratio = action_scores[action].get('vote_ratio', 0)
            summary += f"，基于{vote_ratio:.0%}的历史样本匹配"
    
    return summary


@api.post("/classify", responses={"200": TrajectoryClassificationResponse})
def classify_trajectory(body: TrajectoryClassificationBody):
    """
    航迹行为分类
    
    ## 功能描述
    接收舰船名称和12小时航迹数据，返回预测的行为类型
    
    ## 支持的行为类型
    - **编队**: 多舰协同作战模式
    - **航渡**: 长距离航行模式  
    - **停靠**: 港口停靠模式
    - **巡航**: 区域巡逻模式
    
    ## 请求参数说明
    - **ship_name**: 舰船名称（必填，1-100字符）
    - **trajectory_coords**: 航迹坐标列表（必填，1-100个坐标点）
      - 格式: [[纬度, 经度], [纬度, 经度], ...]  
      - 纬度范围: -90 到 90
      - 经度范围: -180 到 180
    - **top_k**: 返回最相似的前k个结果（可选，默认5，范围1-20）
    
    ## 性能说明
    - 首次分类可能需要1-5分钟（构建向量数据库）
    - 后续分类通常在几秒内完成
    - 推荐坐标点数: 4-20个（代表12小时航迹）
    """
    start_time = time.time()
    
    try:
        # 检查核心模块是否可用
        if not BEHAVIOR_RECOGNITION_AVAILABLE:
            return response(
                code=500,
                message="航迹行为识别服务不可用，请检查相关依赖",
                data={
                    "success": False,
                    "error": "behavior_recognition_unavailable"
                }
            )
        
        # 转换坐标格式：从 [[lat, lng], ...] 转为 [(lat, lng), ...]
        trajectory_coords = [tuple(coord) for coord in body.trajectory_coords]
        
        # 调用核心分类函数
        result = online_trajectory_classification(
            ship_name=body.ship_name,
            trajectory_coords=trajectory_coords,
            top_k=body.top_k
        )
        
        # 计算处理时间
        processing_time = int((time.time() - start_time) * 1000)
        
        if result.get('success', False):
            # 构建清晰的响应结构
            detailed_result = result.get('detailed_result', {})
            
            # 基础分类结果
            classification = {
                "predicted_action": result['predicted_action'],
                "confidence": round(result['confidence'], 4),
                "confidence_level": _get_confidence_level(result['confidence'])
            }
            
            # 处理信息
            processing_info = {
                "ship_name": result['ship_name'],
                "coordinate_count": len(trajectory_coords),
                "processing_time_ms": processing_time,
                "vector_dimension": detailed_result.get('query_data', {}).get('query_vector', [])
            }
            if isinstance(processing_info["vector_dimension"], list):
                processing_info["vector_dimension"] = len(processing_info["vector_dimension"])
            
            # 详细分析
            analysis = {}
            if 'action_scores' in detailed_result:
                action_scores = detailed_result['action_scores']
                analysis["action_scores"] = {
                    action: {
                        "vote_count": scores.get('vote_count', 0),
                        "vote_ratio": round(scores.get('vote_ratio', 0), 3),
                        "avg_similarity": round(scores.get('avg_similarity', 0), 3),
                        "max_similarity": round(scores.get('max_similarity', 0), 3),
                        "combined_score": round(scores.get('combined_score', 0), 3)
                    }
                    for action, scores in action_scores.items()
                }
            
            # 相似样本
            if 'similar_samples' in detailed_result:
                analysis["similar_samples"] = []
                for sample in detailed_result['similar_samples'][:3]:  # 只返回前3个
                    analysis["similar_samples"].append({
                        'sample_id': sample.get('id', 0),
                        'action': sample.get('action', '未知'),
                        'similarity': round(sample.get('similarity', 0), 3),
                        'metadata': {
                            'vector_dim': sample.get('metadata', {}).get('vector_dim', 0),
                            'coordinate_count': sample.get('metadata', {}).get('coordinate_count', 0)
                        }
                    })
            
            # 构建最终响应
            response_data = {
                "success": True,
                "classification": classification,
                "processing": processing_info,
                "analysis": analysis,
                "summary": _generate_summary(classification, analysis)
            }
            
            return response(data=response_data)
        else:
            # 处理失败情况
            error_message = result.get('message', '分类失败')
            return response(
                code=400,
                message=error_message,
                data={
                    "success": False,
                    "ship_name": body.ship_name,
                    "coordinate_count": len(trajectory_coords),
                    "processing_time_ms": processing_time,
                    "error": "classification_failed"
                }
            )
            
    except ValueError as e:
        return response(
            code=400,
            message=f"输入数据验证失败: {str(e)}",
            data={
                "success": False,
                "error": "validation_error",
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }
        )
    except Exception as e:
        return response(
            code=500,
            message=f"服务器内部错误: {str(e)}",
            data={
                "success": False,
                "error": "internal_error",
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }
        )


@api.get("/health", responses={"200": TrajectoryHealthResponse})
def health_check():
    """
    系统健康检查
    
    ## 功能描述
    返回航迹行为识别系统的状态信息，包括模型可用性和支持的功能
    
    ## 返回信息
    - **status**: 系统状态（healthy/degraded）
    - **version**: 系统版本
    - **model_status**: 各模型组件状态
      - behavior_recognition: 行为识别模块
      - faiss_database: FAISS向量数据库
      - transformers: 变换器模型
      - sentence_transformers: 句子变换器
      - llama_cpp: Llama C++模型
    - **supported_actions**: 支持的行为类型
    - **cached_databases**: 已缓存的数据库数量
    
    ## 使用场景
    - 服务启动后的状态检查
    - 定期健康监控
    - 故障排查
    """
    try:
        # 基础系统信息
        health_data = {
            "status": "healthy" if BEHAVIOR_RECOGNITION_AVAILABLE else "degraded",
            "version": "1.0.0",
            "model_status": {
                "behavior_recognition": "available" if BEHAVIOR_RECOGNITION_AVAILABLE else "unavailable",
                "faiss_database": "available" if FAISS_AVAILABLE else "unavailable",
                "transformers": "available" if TRANSFORMERS_AVAILABLE else "unavailable",
                "sentence_transformers": "available" if SENTENCE_TRANSFORMERS_AVAILABLE else "unavailable",
                "llama_cpp": "available" if LLAMA_CPP_AVAILABLE else "unavailable"
            },
            "supported_actions": ["编队", "航渡", "停靠", "巡航"],
            "max_coordinates": 100,
            "default_precision": 7,
            "coordinate_format": "[[纬度, 经度], ...]"
        }
        
        # 如果核心模块可用，获取更详细的状态
        if BEHAVIOR_RECOGNITION_AVAILABLE:
            try:
                engine = get_online_inference_engine()
                if engine:
                    health_data["cached_databases"] = len(engine.vector_databases)
            except Exception as e:
                health_data["warning"] = f"获取引擎状态失败: {str(e)}"
        
        return response(data=health_data)
        
    except Exception as e:
        return response(
            code=500,
            message=f"健康检查失败: {str(e)}",
            data={
                "status": "error",
                "error": str(e)
            }
        )


@api.get("/database/info", responses={"200": ShipDatabaseResponse})
def get_database_info(query: ShipDatabaseQuery):
    """
    获取向量数据库信息
    
    ## 功能描述
    返回系统中存储的舰船历史数据统计信息
    
    ## 查询参数（可选）
    - **ship_name**: 舰船名称过滤（可选，最大100字符）
    - **action_filter**: 行为类型过滤（可选，编队/航渡/停靠/巡航）
    - **limit**: 返回记录数限制（可选，默认10，范围1-100）
    
    ## 返回信息
    - **database_info**: 数据库基础信息
      - database_type: 数据库类型（FAISS/Basic）
      - cached_databases: 已缓存数据库数量
      - total_vectors: 总向量数量
      - unique_actions: 唯一行为类型数
      - unique_ships: 唯一舰船数
      - vector_dimension: 向量维度
    - **behavior_stats**: 各行为类型统计
    - **ships**: 已缓存的舰船列表
    
    ## 使用场景
    - 了解系统数据规模
    - 检查特定舰船数据状态
    - 数据库性能监控
    """
    try:
        if not BEHAVIOR_RECOGNITION_AVAILABLE:
            return response(
                code=503,
                message="航迹行为识别服务不可用",
                data={"error": "service_unavailable"}
            )
        
        # 获取在线推理引擎
        engine = get_online_inference_engine()
        
        # 基础响应数据
        database_info = {
            "database_info": {
                "cached_databases": len(engine.vector_databases),
                "database_type": "FAISS" if FAISS_AVAILABLE else "Basic",
                "supported_actions": ["编队", "航渡", "停靠", "巡航"]
            },
            "ships": list(engine.vector_databases.keys()) if engine.vector_databases else []
        }
        
        # 如果有缓存的数据库，获取详细统计信息
        if engine.vector_databases:
            # 尝试获取第一个数据库的详细信息作为示例
            sample_db_name = list(engine.vector_databases.keys())[0]
            sample_db = engine.vector_databases[sample_db_name]
            
            try:
                db_info = sample_db.get_database_info()
                database_info["database_info"].update({
                    "total_vectors": db_info.get("total_vectors", 0),
                    "unique_actions": db_info.get("unique_actions", 0),
                    "unique_ships": db_info.get("unique_ships", 0),
                    "vector_dimension": db_info.get("vector_dimension", "unknown")
                })
                
                # 获取行为统计信息
                if hasattr(sample_db, 'get_behavior_stats'):
                    behavior_stats = sample_db.get_behavior_stats()
                    database_info["behavior_stats"] = behavior_stats
                    
            except Exception as e:
                database_info["warning"] = f"获取数据库详细信息失败: {str(e)}"
        
        return response(data=database_info)
        
    except Exception as e:
        return response(
            code=500,
            message=f"获取数据库信息失败: {str(e)}",
            data={"error": "database_info_error"}
        )


@api.post("/database/rebuild", responses={"200": DatabaseRebuildResponse})
def rebuild_ship_database(body: DatabaseRebuildBody):
    """
    重建指定舰船的向量数据库
    
    ## 功能描述
    强制重新获取历史数据并重建向量数据库（管理员功能）
    
    ## 请求参数说明
    - **ship_name**: 要重建数据库的舰船名称（必填，1-100字符）
    
    ## 处理流程
    1. 从在线接口获取舰船历史数据
    2. 按行为类型进行数据分块
    3. 生成时间窗口和坐标序列
    4. 进行文本向量化处理
    5. 构建高性能FAISS向量数据库
    6. 缓存到系统中供后续查询使用
    
    ## 性能说明
    - **处理时间**: 2-10分钟（取决于历史数据量）
    - **数据来源**: 实时从在线接口获取
    - **存储方式**: FAISS向量数据库 + SQLite元数据
    - **缓存策略**: 自动缓存，提升后续查询速度
    
    ## 使用场景
    - 新舰船首次添加到系统
    - 历史数据更新后需要重建
    - 数据库损坏或异常时修复
    - 系统升级后的数据迁移
    
    ## 注意事项
    ⚠️ 此操作耗时较长，请耐心等待
    ⚠️ 建议在系统空闲时执行
    ⚠️ 重建期间会覆盖现有数据库
    """
    try:
        if not BEHAVIOR_RECOGNITION_AVAILABLE:
            return response(
                code=503,
                message="航迹行为识别服务不可用",
                data={"error": "service_unavailable"}
            )
        
        ship_name = body.ship_name
        if not ship_name:
            return response(
                code=400,
                message="缺少参数: ship_name",
                data={"error": "missing_ship_name"}
            )
        
        # 获取在线推理引擎
        engine = get_online_inference_engine()
        
        # 强制重建数据库
        start_time = time.time()
        vector_db = engine.load_or_build_ship_database(ship_name, force_rebuild=True)
        build_time = int((time.time() - start_time) * 1000)
        
        if vector_db:
            db_info = vector_db.get_database_info()
            return response(data={
                "success": True,
                "ship_name": ship_name,
                "build_time_ms": build_time,
                "database_info": db_info
            })
        else:
            return response(
                code=400,
                message=f"重建 {ship_name} 数据库失败",
                data={
                    "success": False,
                    "ship_name": ship_name,
                    "build_time_ms": build_time
                }
            )
            
    except Exception as e:
        return response(
            code=500,
            message=f"重建数据库失败: {str(e)}",
            data={"error": "rebuild_error"}
        )