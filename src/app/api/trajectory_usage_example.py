# -*- coding: utf-8 -*-
"""
航迹行为识别 API 使用示例

这个文件展示了如何使用航迹行为识别API接口进行舰船行为分类。
"""

import requests
import json
from typing import List, Tuple


class TrajectoryAPI:
    """航迹行为识别API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:5000", api_token: str = None):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器地址
            api_token: 认证令牌（如果需要）
        """
        self.base_url = base_url.rstrip('/')
        self.api_prefix = "/api/v1/trajectory"
        self.headers = {
            'Content-Type': 'application/json'
        }
        
        if api_token:
            self.headers['Authorization'] = f'Bearer {api_token}'
    
    def classify_trajectory(self, ship_name: str, trajectory_coords: List[List[float]], top_k: int = 5) -> dict:
        """
        航迹行为分类
        
        Args:
            ship_name: 舰船名称
            trajectory_coords: 航迹坐标列表 [[纬度, 经度], ...]
            top_k: 返回最相似的前k个结果
            
        Returns:
            分类结果字典
        """
        url = f"{self.base_url}{self.api_prefix}/classify"
        
        payload = {
            "ship_name": ship_name,
            "trajectory_coords": trajectory_coords,
            "top_k": top_k
        }
        
        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=30)
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def health_check(self) -> dict:
        """
        系统健康检查
        
        Returns:
            系统状态信息
        """
        url = f"{self.base_url}{self.api_prefix}/health"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def get_database_info(self, ship_name: str = None, action_filter: str = None, limit: int = 10) -> dict:
        """
        获取向量数据库信息
        
        Args:
            ship_name: 舰船名称过滤
            action_filter: 行为类型过滤
            limit: 返回记录数限制
            
        Returns:
            数据库信息
        """
        url = f"{self.base_url}{self.api_prefix}/database/info"
        
        params = {}
        if ship_name:
            params['ship_name'] = ship_name
        if action_filter:
            params['action_filter'] = action_filter
        if limit != 10:
            params['limit'] = limit
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def rebuild_database(self, ship_name: str) -> dict:
        """
        重建指定舰船的向量数据库
        
        Args:
            ship_name: 舰船名称
            
        Returns:
            重建结果
        """
        url = f"{self.base_url}{self.api_prefix}/database/rebuild"
        
        payload = {"ship_name": ship_name}
        
        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=60)
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}


def example_usage():
    """API使用示例"""
    
    # 初始化API客户端
    api = TrajectoryAPI(base_url="http://localhost:5000")
    
    print("=== 航迹行为识别API使用示例 ===\n")
    
    # 1. 系统健康检查
    print("1. 系统健康检查")
    health = api.health_check()
    print(json.dumps(health, ensure_ascii=False, indent=2))
    print()
    
    # 2. 航迹分类示例 - 巡航行为
    print("2. 航迹分类示例 - 巡航行为")
    ship_name = "文森号航空母舰"
    
    # 模拟12小时的巡航航迹（移动轨迹）
    cruise_trajectory = [
        [35.1234, 139.5678],  # 日本海域
        [35.1244, 139.5688],
        [35.1254, 139.5698],
        [35.1264, 139.5708],
        [35.1274, 139.5718],
        [35.1284, 139.5728],
        [35.1294, 139.5738],
        [35.1304, 139.5748]
    ]
    
    result = api.classify_trajectory(ship_name, cruise_trajectory, top_k=5)
    print(f"舰船: {ship_name}")
    print(f"坐标点数: {len(cruise_trajectory)}")
    print("分类结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()
    
    # 3. 航迹分类示例 - 停靠行为
    print("3. 航迹分类示例 - 停靠行为")
    
    # 模拟停靠航迹（位置相对固定）
    docking_trajectory = [
        [36.8485, -76.2951],  # 诺福克海军基地
        [36.8485, -76.2951],
        [36.8486, -76.2952],
        [36.8485, -76.2951],
        [36.8484, -76.2950],
        [36.8485, -76.2951]
    ]
    
    result = api.classify_trajectory(ship_name, docking_trajectory, top_k=3)
    print(f"舰船: {ship_name}")
    print(f"坐标点数: {len(docking_trajectory)}")
    print("分类结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()
    
    # 4. 获取数据库信息
    print("4. 获取数据库信息")
    db_info = api.get_database_info()
    print(json.dumps(db_info, ensure_ascii=False, indent=2))
    print()
    
    # 5. 错误处理示例
    print("5. 错误处理示例")
    
    # 无效的坐标数据
    invalid_trajectory = [
        [200, 300],  # 超出经纬度范围
        [35.1234, 139.5678]
    ]
    
    result = api.classify_trajectory(ship_name, invalid_trajectory)
    print("无效坐标的响应:")
    print(json.dumps(result, ensure_ascii=False, indent=2))


def create_custom_trajectory(start_lat: float, start_lng: float, 
                           movement_type: str = "linear", 
                           points: int = 8) -> List[List[float]]:
    """
    创建自定义航迹数据
    
    Args:
        start_lat: 起始纬度
        start_lng: 起始经度
        movement_type: 移动类型 ("linear", "circular", "stationary")
        points: 坐标点数量
        
    Returns:
        航迹坐标列表
    """
    trajectory = []
    
    if movement_type == "stationary":
        # 停靠模式：位置基本不变，有微小波动
        import random
        for i in range(points):
            lat = start_lat + random.uniform(-0.0001, 0.0001)
            lng = start_lng + random.uniform(-0.0001, 0.0001)
            trajectory.append([lat, lng])
    
    elif movement_type == "linear":
        # 直线航行
        lat_step = 0.001  # 每步纬度变化
        lng_step = 0.001  # 每步经度变化
        
        for i in range(points):
            lat = start_lat + i * lat_step
            lng = start_lng + i * lng_step
            trajectory.append([lat, lng])
    
    elif movement_type == "circular":
        # 圆形巡航
        import math
        radius = 0.01  # 巡航半径
        
        for i in range(points):
            angle = 2 * math.pi * i / points
            lat = start_lat + radius * math.cos(angle)
            lng = start_lng + radius * math.sin(angle)
            trajectory.append([lat, lng])
    
    return trajectory


# API接口端点说明
API_ENDPOINTS = {
    "航迹分类": {
        "method": "POST",
        "url": "/api/v1/trajectory/classify",
        "description": "对输入的航迹数据进行行为分类",
        "request_body": {
            "ship_name": "舰船名称 (string)",
            "trajectory_coords": "坐标列表 [[纬度, 经度], ...] (array)",
            "top_k": "返回相似结果数量 (integer, 可选, 默认5)"
        },
        "response": {
            "success": "是否成功 (boolean)",
            "predicted_action": "预测行为 (string: 编队/航渡/停靠/巡航)",
            "confidence": "置信度 (float: 0-1)",
            "explanation": "分类解释 (string)",
            "processing_time_ms": "处理时间毫秒 (integer)"
        }
    },
    "健康检查": {
        "method": "GET",
        "url": "/api/v1/trajectory/health",
        "description": "检查系统状态和模型可用性",
        "response": {
            "status": "系统状态 (string)",
            "model_status": "各模型状态 (object)",
            "supported_actions": "支持的行为类型 (array)"
        }
    },
    "数据库信息": {
        "method": "GET",
        "url": "/api/v1/trajectory/database/info",
        "description": "获取向量数据库统计信息",
        "query_params": {
            "ship_name": "舰船名称过滤 (string, 可选)",
            "action_filter": "行为类型过滤 (string, 可选)",
            "limit": "返回记录数 (integer, 可选)"
        }
    },
    "重建数据库": {
        "method": "POST",
        "url": "/api/v1/trajectory/database/rebuild",
        "description": "强制重建指定舰船的向量数据库",
        "request_body": {
            "ship_name": "舰船名称 (string)"
        }
    }
}


if __name__ == "__main__":
    # 运行使用示例
    example_usage()
    
    # 打印API端点说明
    print("\n=== API端点说明 ===")
    for name, info in API_ENDPOINTS.items():
        print(f"\n【{name}】")
        print(f"方法: {info['method']}")
        print(f"URL: {info['url']}")
        print(f"说明: {info['description']}")
        
        if 'request_body' in info:
            print("请求体:")
            for field, desc in info['request_body'].items():
                print(f"  - {field}: {desc}")
        
        if 'query_params' in info:
            print("查询参数:")
            for param, desc in info['query_params'].items():
                print(f"  - {param}: {desc}")
        
        if 'response' in info:
            print("响应字段:")
            for field, desc in info['response'].items():
                print(f"  - {field}: {desc}")