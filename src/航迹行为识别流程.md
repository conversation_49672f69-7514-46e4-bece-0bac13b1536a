

------



###  阶段一：历史数据准备



1. **数据解析 (Data Parsing)**

   - 从接口获取舰船历史轨迹信息res。
   - 我们的核心数据源是res[`trace_data`]这个列表，因为它同时包含了坐标(`lat`, `lng`)和行为标签(`action`)。
   - 再从中筛选出我们需要的行为活动-["编队","航渡","停靠","巡航"]

2. **按“Action”进行事件分块 (Event Segmentation by Action)**

   - 遍历筛选后的列表，将连续相同的`action`的航迹点合并成一个个“事件区块”。
   - 例如，您会得到一个包含连续几十个“停靠”点的区块，一个包含上百个“巡航”点的区块，等等。这样做保证了每个区块内的行为是纯粹的。

3. **固定长度滑窗切片 (Fixed-Length Sliding Window)**

   - 在**每一个**上一步得到的纯粹“事件区块”内部，使用一个12小时的滑动窗口进行数据切片。

   - **窗口大小**：12小时。

   - **步长（Stride）**：可以设置为1小时或2小时。步长越小，生成的样本越多，数据库也越大。

   - **结果**：您会得到成千上万个12小时长的、并且行为标签100%纯粹的航迹片段。例如，从一个持续3天的“停靠”事件中，您可以切分出几十个12小时的“停靠”片段。

   - **对于早期数据**：一个12小时的窗口内，可能只包含一个数据点。这虽然信息量少，但依然可以代表当时的行为状态。

     **对于后期数据**：一个12小时的窗口内，最多可以包含 `12 / 2 = 6` 个航迹点。这形成了一个更丰富的序列，能够让模型更好地学习到这12小时内的细微移动模式，从而生成更具代表性的行为向量。

4. **坐标文本化 (Coordinate Textualization)**

   - 对**每一个**12小时的航迹片段，将其中的经纬度坐标序列，通过 **Geohash** 算法转换成一个文本字符串。
   - **Geohash精度**：选择一个合适的精度（例如6-8位），这个长度将作为一个可以调整的参数。
   - **结果**：每个航迹片段都变成了一个类似`"wx4g8e wx4g8f wx4g8g..."`的“单词”序列。

5. - **文本向量化 (Text Vectorization) - (核心修改)**
     - **a. 构建包含身份的输入文本**：
       - 对于每一个航迹片段，我们从其元数据中提取出**舰船名称**（例如`"文森号航空母舰"`）。
       - 将**舰船名称**与**Geohash句子**组合成一个新的、信息更丰富的输入字符串。推荐使用一个特殊的分隔符（如空格或`[SEP]`，对于现代模型，空格通常足够）来连接它们。
       - **格式示例**：`"文森号航空母舰 wx4g8e wx4g8f wx4g8g..."`
     - **b. 加载专向向量化模型**：
       - 加载 `Qwen/Qwen3-Embedding-0.6B` 模型（或您选择的其他高质量Embedding模型）。
     - **c. 生成向量**：
       - 将上一步**组合好**的字符串输入到模型中，生成一个固定维度的高质量向量。现在，这个向量同时编码了“**谁**”和“**做了什么**”的信息。

6. **构建并存储向量数据库 (Build and Store Vector Database)**

   - 将所有生成的向量及其对应的元数据（`metadata`）存储起来。
   - **存储内容**：
     - **向量本身** (Vector)
     - **行为标签** (Label)：例如“停靠”、“巡航”等。
     - **辅助信息** (Metadata)：例如，该片段对应的原始开始/结束时间、所属的地理区域名称（如“圣迭戈海军基地”）等，这些信息在做可解释性展示时非常有用。
   - **存储方式**：对于大规模数据，建议使用专门的向量数据库如 **FAISS** 或 **Milvus** 来构建索引，以实现毫秒级的快速检索。

------



### ### 阶段二：在线查询 (Online Inference)



**目标：** 接收用户输入的新航迹，并实时判断其行为。

1. **接收输入 (Receive Input)**

   - 获取用户输入的两个信息：舰船名称（例如“文森号航空母舰”）和最新的12小时航迹点（经纬度坐标列表）。

2. **预处理新航迹 (Preprocess New Track)**

   - 对新输入的12小时航迹执行与**离线准备阶段完全相同**的预处理流程：
     - **a. 坐标文本化**：使用完全相同的 **Geohash** 精度，将12小时的坐标点转换成“Geohash句子”。
     - **b.将用户输入的舰船名称 `ship_A` 与这个“Geohash句子”组合成与离线处理时**完全相同格式**的字符串：`"ship_A Geohash句子..."`。
     - **c. 文本向量化**：使用**同一个**`Qwen3-Embedding`模型，将“Geohash句子”转换成一个**查询向量 (Query Vector)**。

3. **相似度搜索 (Similarity Search)**

   - 拿着这个**查询向量**，在第一阶段构建好的“历史行为向量数据库”中进行高速搜索。
   - **搜索算法**：计算查询向量与数据库中所有向量的**余弦相似度**。
   - **搜索目标**：找出相似度得分最高的 **Top-K** 个历史航迹片段（K是一个可调参数，通常设为3、5或7）。
   - **关键点**：因为现在每个历史向量都包含了船的身份信息，所以如果输入的航母轨迹与历史上另一艘航母的相似模式匹配度更高，系统也能准确地找出来。

4. **决策与输出 (Decision and Output)**

   - **a. 投票决策**：查看这Top-K个最相似的历史片段的**行为标签**，采用“少数服从多数”的原则，确定最终的预测行为。例如，如果5个最相似的片段中有4个的标签是“巡航”，那么新航迹的行为就被判定为“巡航”。

   - **b. 生成可解释的结论**：向用户展示最终的判断结果，并附上最有力的证据作为解释。可以清晰地告诉用户，这个行为是基于与**哪艘船**的**哪个历史片段**最相似而判断出来的。

     - **输出示例**：

       > **预测行为：** 直线巡航
       >
       > **判断依据：** 该航迹模式与“文森号航空母舰”历史上的5个航迹片段高度相似（相似度高达98.7%）。这些相似的历史片段主要发生在以下时间和地点：
       >
       > - 2022年2月12日，在阿留申群岛南部海域巡航
       > - 2023年4月7日，在加利福尼亚南部海域巡航
       > - ...（列出其他证据）

这个完整的流程确保了从原始数据处理到最终结果输出的每一步都是清晰、连贯且高效的。