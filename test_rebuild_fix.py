#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库重建接口修复
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_models():
    """测试新的Pydantic模型"""
    print("🧪 测试数据库重建模型...")
    
    try:
        # 测试导入
        from app.form.trajectory import DatabaseRebuildBody, DatabaseRebuildResponse
        print("✅ 模型导入成功")
        
        # 测试创建请求模型
        rebuild_request = DatabaseRebuildBody(ship_name="测试舰船")
        print(f"✅ 请求模型创建成功: {rebuild_request.ship_name}")
        
        # 测试模型验证
        try:
            invalid_request = DatabaseRebuildBody(ship_name="")  # 空字符串应该失败
            print("❌ 验证失败：空字符串应该被拒绝")
        except ValueError:
            print("✅ 模型验证正常工作")
        
        # 测试API模块导入
        from app.api.trajectory import api
        print("✅ API模块导入成功")
        
        print("\n🎉 所有测试都通过了！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 数据库重建接口修复验证")
    print("=" * 40)
    
    success = test_models()
    
    if success:
        print("\n✅ 修复成功！现在可以正常启动API服务了。")
        print("运行命令: python start_api_server.py")
    else:
        print("\n❌ 还有问题需要解决。")