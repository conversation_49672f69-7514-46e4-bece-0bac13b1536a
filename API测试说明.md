# 航迹行为识别API测试说明

## 概述

本项目提供了航迹行为识别的API服务，可以对舰船的12小时航迹数据进行行为分类，支持4种行为类型：**编队**、**航渡**、**停靠**、**巡航**。

## API接口列表

### 1. 系统健康检查
- **接口**: `GET /api/v1/trajectory/health`
- **功能**: 检查系统状态和模型可用性
- **参数**: 无
- **返回**: 系统状态、版本信息、模型状态等

### 2. 数据库信息查询
- **接口**: `GET /api/v1/trajectory/database/info`
- **功能**: 获取向量数据库的统计信息
- **参数**: 可选的查询参数
- **返回**: 数据库类型、缓存信息、支持的行为类型等

### 3. 航迹行为分类 ⭐
- **接口**: `POST /api/v1/trajectory/classify`
- **功能**: 对输入的航迹数据进行行为分类
- **参数**: 
  ```json
  {
    "ship_name": "舰船名称",
    "trajectory_coords": [[纬度, 经度], [纬度, 经度], ...],
    "top_k": 5
  }
  ```
- **返回**: 预测的行为类型、置信度、相似样本等

### 4. 数据库重建
- **接口**: `POST /api/v1/trajectory/database/rebuild`
- **功能**: 重建指定舰船的向量数据库
- **参数**: 
  ```json
  {
    "ship_name": "舰船名称"
  }
  ```
- **返回**: 重建结果和数据库信息

## 测试方法

### 方法1: 使用快速测试脚本 (推荐)

```bash
# 运行快速测试
python quick_test_api.py
```

这个脚本会快速验证所有API接口是否正常工作。

### 方法2: 使用完整测试脚本

```bash
# 运行完整测试
python test_trajectory_api.py
```

这个脚本提供了更详细的测试，包括多种测试用例和交互式选项。

### 方法3: 使用curl命令

#### 健康检查
```bash
curl -X GET "http://localhost:5000/api/v1/trajectory/health" \
     -H "Content-Type: application/json"
```

#### 航迹分类
```bash
curl -X POST "http://localhost:5000/api/v1/trajectory/classify" \
     -H "Content-Type: application/json" \
     -d '{
       "ship_name": "文森号航空母舰",
       "trajectory_coords": [
         [35.1234, 139.5678],
         [35.1244, 139.5688],
         [35.1254, 139.5698],
         [35.1264, 139.5708]
       ],
       "top_k": 3
     }'
```

#### 数据库信息
```bash
curl -X GET "http://localhost:5000/api/v1/trajectory/database/info" \
     -H "Content-Type: application/json"
```

### 方法4: 使用Postman或其他API测试工具

导入以下API配置到Postman：

1. **Base URL**: `http://localhost:5000`
2. **Headers**: `Content-Type: application/json`
3. 按照上述接口列表创建对应的请求

## 测试用例示例

### 线性航迹 (航渡行为)
```json
{
  "ship_name": "文森号航空母舰",
  "trajectory_coords": [
    [35.1234, 139.5678],
    [35.1244, 139.5688],
    [35.1254, 139.5698],
    [35.1264, 139.5708],
    [35.1274, 139.5718],
    [35.1284, 139.5728]
  ],
  "top_k": 5
}
```

### 圆形航迹 (巡航行为)
```json
{
  "ship_name": "企业号航空母舰",
  "trajectory_coords": [
    [36.0, 140.0],
    [36.007, 140.007],
    [36.0, 140.01],
    [35.993, 140.007],
    [35.993, 139.993],
    [36.0, 139.99],
    [36.007, 139.993],
    [36.01, 140.0]
  ],
  "top_k": 3
}
```

### 静止航迹 (停靠行为)
```json
{
  "ship_name": "辽宁舰",
  "trajectory_coords": [
    [36.8485, -76.2951],
    [36.8485, -76.2951],
    [36.8486, -76.2950],
    [36.8484, -76.2952],
    [36.8485, -76.2951],
    [36.8485, -76.2951]
  ],
  "top_k": 5
}
```

## 启动API服务

在测试之前，确保API服务正在运行：

```bash
# 进入项目根目录
cd /path/to/flask-api-demo

# 安装依赖
pip install -r requirements.txt

# 启动服务
python src/wsgi.py
```

服务启动后，应该可以在 `http://localhost:5000` 访问API。

## 预期响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "ship_name": "文森号航空母舰",
    "predicted_action": "航渡",
    "confidence": 0.8542,
    "coordinate_count": 6,
    "processing_time_ms": 1234,
    "explanation": "详细的分类解释...",
    "action_scores": {
      "航渡": {"combined_score": 0.8542, "vote_count": 3},
      "巡航": {"combined_score": 0.1234, "vote_count": 1}
    },
    "similar_samples": [
      {"id": 42, "similarity": 0.9123, "action": "航渡"},
      {"id": 15, "similarity": 0.8876, "action": "航渡"}
    ]
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "输入数据验证失败: 坐标点数量不能为空",
  "data": {
    "success": false,
    "error": "validation_error",
    "processing_time_ms": 12
  }
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查API服务是否启动: `python src/wsgi.py`
   - 检查端口是否被占用: `netstat -an | grep 5000`

2. **模块导入失败**
   - 检查 `behavior recognition.py` 文件是否存在
   - 检查依赖包是否安装: `pip install -r requirements.txt`

3. **分类失败**
   - 检查输入数据格式是否正确
   - 检查坐标数据是否在有效范围内 (纬度: -90到90, 经度: -180到180)

4. **处理时间过长**
   - 首次分类可能需要下载和构建向量数据库，耗时较长
   - 后续分类会使用缓存，速度会明显提升

### 调试建议

1. **查看服务日志**: 运行API服务时观察控制台输出
2. **检查健康状态**: 首先调用健康检查接口确认服务状态
3. **从简单测试开始**: 使用快速测试脚本进行初步验证
4. **逐步增加复杂度**: 先测试简单数据，再测试复杂场景

## 性能说明

- **首次分类**: 可能需要1-5分钟 (需要构建向量数据库)
- **后续分类**: 通常在几秒内完成
- **数据库重建**: 可能需要几分钟时间
- **支持的坐标点数**: 1-100个点
- **推荐的坐标点数**: 4-20个点 (代表12小时航迹)

## 技术栈

- **后端框架**: Flask + Flask-OpenAPI3
- **向量数据库**: FAISS (高性能) 或基础向量数据库
- **文本向量化**: Qwen3-Embedding 或 sentence-transformers
- **坐标处理**: Geohash编码
- **数据存储**: SQLite + Pickle

## 联系支持

如果在测试过程中遇到问题，请检查：
1. 项目文件是否完整
2. Python环境和依赖是否正确安装
3. API服务是否正常启动
4. 网络连接是否正常